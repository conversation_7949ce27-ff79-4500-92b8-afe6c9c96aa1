{"c": ["app/layout", "app/page", "webpack"], "r": [], "m": ["(app-pages-browser)/./node_modules/next/dist/api/image.js", "(app-pages-browser)/./node_modules/next/dist/client/image-component.js", "(app-pages-browser)/./node_modules/next/dist/client/use-merged-ref.js", "(app-pages-browser)/./node_modules/next/dist/compiled/picomatch/index.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/amp-mode.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/get-img-props.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/head.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-blur-svg.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-config.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-external.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/image-loader.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-local-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/match-remote-pattern.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/router-context.shared-runtime.js", "(app-pages-browser)/./node_modules/next/dist/shared/lib/side-effect.js"]}