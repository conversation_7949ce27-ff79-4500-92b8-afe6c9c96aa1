# 🛍️ Shopee Product Recommendations

เว็บไซต์แนะนำสินค้าจาก Shopee Affiliate API พัฒนาด้วย Next.js, TypeScript และ Tailwind CSS

## ✨ Features

- 🔍 **ค้นหาสินค้า** - ค้นหาสินค้าด้วยคำค้นหา
- 💰 **แสดงคอมมิชชั่น** - แสดงอัตราคอมมิชชั่นของแต่ละสินค้า
- 🏷️ **ราคาและส่วนลด** - แสดงราคาเดิม ราคาปัจจุบัน และส่วนลด
- ⭐ **คะแนนและยอดขาย** - แสดงคะแนนรีวิวและยอดขาย
- 🔗 **Short Link** - สร้างลิงก์สั้นสำหรับแชร์
- 📱 **Responsive Design** - รองรับทุกขนาดหน้าจอ
- 🎨 **สวยงาม** - UI/UX ที่ทันสมัยด้วย Tailwind CSS

## 🛠️ Tech Stack

- **Framework**: Next.js 15 with App Router
- **Language**: TypeScript
- **Styling**: Tailwind CSS
- **API**: Shopee Affiliate GraphQL API
- **Images**: Unsplash API for product images

## 🚀 Getting Started

### Prerequisites

- Node.js 18+
- npm หรือ yarn

### Installation

1. Clone the repository
```bash
git clone <repository-url>
cd shopeeproduct
```

2. Install dependencies
```bash
npm install
```

3. Set up environment variables
```bash
cp .env.local.example .env.local
```

Edit `.env.local`:
```env
SHOPEE_APP_ID=your_app_id
SHOPEE_SECRET=your_secret_key
SHOPEE_API_ENDPOINT=https://open-api.affiliate.shopee.co.th/graphql
```

4. Run the development server
```bash
npm run dev
```

5. Open [http://localhost:3000](http://localhost:3000)

## 📁 Project Structure

```
src/
├── app/
│   ├── api/shopee/          # API routes
│   │   ├── offers/          # Product offers endpoint
│   │   ├── brand-offers/    # Brand offers endpoint
│   │   ├── short-link/      # Short link generation
│   │   └── conversion-report/ # Conversion reports
│   ├── globals.css          # Global styles
│   ├── layout.tsx           # Root layout
│   └── page.tsx             # Home page
├── components/
│   ├── ProductCard.tsx      # Product card component
│   ├── SearchBar.tsx        # Search functionality
│   └── Loading.tsx          # Loading components
└── lib/
    ├── shopee-api.ts        # Shopee API client
    ├── types.ts             # TypeScript types
    └── utils.ts             # Utility functions
```

## 🔧 API Endpoints

### GET /api/shopee/offers
ค้นหาสินค้า
- Query parameters: `keyword`, `minPrice`, `maxPrice`, `sortBy`, `limit`

### GET /api/shopee/brand-offers
ดึงข้อมูลแบรนด์ออฟเฟอร์

### POST /api/shopee/short-link
สร้างลิงก์สั้น
- Body: `{ "originalLink": "https://..." }`

### GET /api/shopee/conversion-report
รายงานการแปลง
- Query parameters: `purchaseTimeStart`, `purchaseTimeEnd`, `scrollId`

## 🎯 Shopee Affiliate API Integration

โปรเจ็กต์นี้ใช้ Shopee Affiliate API v2 พร้อมระบบ authentication SHA256:

- **Authentication**: SHA256 signature with timestamp
- **Rate Limit**: 2000 requests/hour
- **GraphQL**: ใช้ GraphQL สำหรับ API calls
- **Pagination**: รองรับ scrollId สำหรับข้อมูลหลายหน้า

## 🔐 Authentication

API ใช้ SHA256 signature authentication:
```
Authorization: SHA256 Credential={AppId}, Timestamp={Timestamp}, Signature={Signature}
```

Signature = SHA256(AppId + Timestamp + Payload + Secret)

## 🎨 UI Components

- **ProductCard**: แสดงข้อมูลสินค้าพร้อมภาพ ราคา คอมมิชชั่น
- **SearchBar**: ฟอร์มค้นหาพร้อมตัวกรอง
- **Loading**: Skeleton loading states

## 📱 Responsive Design

- Mobile-first approach
- Breakpoints: sm, md, lg, xl
- Grid layout ปรับตามขนาดหน้าจอ

## 🚀 Deployment

### Vercel (Recommended)
```bash
npm run build
vercel --prod
```

### Other Platforms
```bash
npm run build
npm start
```

## 🤝 Contributing

1. Fork the project
2. Create feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Open Pull Request

## 📄 License

This project is licensed under the MIT License.

## 📞 Support

หากมีปัญหาหรือข้อสงสัย กรุณาติดต่อ:
- Email: <EMAIL>
- GitHub Issues: [Create an issue](https://github.com/your-username/shopeeproduct/issues)
