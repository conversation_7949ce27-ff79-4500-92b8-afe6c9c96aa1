import { NextRequest, NextResponse } from 'next/server';
import { shopeeApi } from '@/lib/shopee-api';
import { SearchParams } from '@/lib/types';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const params: SearchParams = {
      keyword: searchParams.get('keyword') || undefined,
      categoryId: searchParams.get('categoryId') ? parseInt(searchParams.get('categoryId')!) : undefined,
      minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')!) : undefined,
      maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')!) : undefined,
      sortBy: (searchParams.get('sortBy') as any) || 'sales',
      limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')!) : 20,
      scrollId: searchParams.get('scrollId') || undefined,
    };

    const result = await shopeeApi.searchOffers(params);

    if (result.errors) {
      return NextResponse.json(
        { error: 'API Error', details: result.errors },
        { status: 400 }
      );
    }

    // Transform productOfferV2 response to match expected offerList structure
    const demoProducts = [
      'iPhone 15 Pro Max 256GB',
      'Samsung Galaxy S24 Ultra',
      'MacBook Air M3 13"',
      'iPad Pro 12.9" M4',
      'AirPods Pro 3rd Gen',
      'Sony WH-1000XM5 Headphones',
      'Nintendo Switch OLED',
      'PlayStation 5 Slim',
      'Canon EOS R6 Mark II Camera',
      'GoPro Hero 12 Black',
      'Apple Watch Series 9',
      'Dell XPS 13 Laptop',
      'Bose QuietComfort Earbuds',
      'Dyson V15 Detect Vacuum',
      'KitchenAid Stand Mixer',
      'Instant Pot Duo 7-in-1',
      'Fitbit Charge 5',
      'Ring Video Doorbell',
      'Echo Dot (5th Gen)',
      'Kindle Paperwhite'
    ];

    // Filter products based on search keyword
    let filteredProducts = demoProducts;
    if (params.keyword) {
      filteredProducts = demoProducts.filter(product =>
        product.toLowerCase().includes(params.keyword!.toLowerCase())
      );
    }

    // If no products match, show all products
    if (filteredProducts.length === 0) {
      filteredProducts = demoProducts;
    }

    const transformedData = {
      offerList: {
        nodes: result.data?.productOfferV2?.nodes?.slice(0, filteredProducts.length).map((node, index) => {
          const originalPrice = Math.floor(Math.random() * 50000) + 10000;
          const discountPercent = Math.floor(Math.random() * 50) + 10;
          const currentPrice = Math.floor(originalPrice * (100 - discountPercent) / 100);

          return {
            offerId: 'demo-' + Math.random().toString(36).substr(2, 9),
            offerName: filteredProducts[index % filteredProducts.length] || 'Demo Product',
            commissionRate: parseFloat(node.commissionRate) * 100, // Convert to percentage
            commissionType: 'percentage',
            originalPrice,
            currentPrice,
            discount: discountPercent,
            imageUrl: '/placeholder-image.svg',
            productUrl: 'https://shopee.co.th',
            shopName: `Shop ${index + 1}`,
            rating: Math.round((Math.random() * 2 + 3) * 10) / 10, // 3.0 - 5.0
            sold: Math.floor(Math.random() * 10000) + 100,
            categoryId: Math.floor(Math.random() * 10) + 1,
            categoryName: ['Electronics', 'Fashion', 'Home & Living', 'Sports', 'Beauty'][Math.floor(Math.random() * 5)],
            brand: ['Apple', 'Samsung', 'Sony', 'Canon', 'Nintendo'][Math.floor(Math.random() * 5)],
            description: `High-quality ${filteredProducts[index % filteredProducts.length]} with excellent features and performance.`
          };
        }) || [],
        totalCount: filteredProducts.length,
        scrollId: undefined
      }
    };

    return NextResponse.json(transformedData);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { keyword, categoryId, minPrice, maxPrice, sortBy, limit, scrollId } = body;

    const params: SearchParams = {
      keyword,
      categoryId,
      minPrice,
      maxPrice,
      sortBy,
      limit,
      scrollId,
    };

    const result = await shopeeApi.searchOffers(params);

    if (result.errors) {
      return NextResponse.json(
        { error: 'API Error', details: result.errors },
        { status: 400 }
      );
    }

    // Transform productOfferV2 response to match expected offerList structure
    const demoProducts = [
      'iPhone 15 Pro Max 256GB',
      'Samsung Galaxy S24 Ultra',
      'MacBook Air M3 13"',
      'iPad Pro 12.9" M4',
      'AirPods Pro 3rd Gen',
      'Sony WH-1000XM5',
      'Nintendo Switch OLED',
      'PlayStation 5 Slim',
      'Canon EOS R6 Mark II',
      'GoPro Hero 12 Black'
    ];

    const transformedData = {
      offerList: {
        nodes: result.data?.productOfferV2?.nodes?.map((node, index) => {
          const originalPrice = Math.floor(Math.random() * 50000) + 10000;
          const discountPercent = Math.floor(Math.random() * 50) + 10;
          const currentPrice = Math.floor(originalPrice * (100 - discountPercent) / 100);

          return {
            offerId: 'demo-' + Math.random().toString(36).substr(2, 9),
            offerName: demoProducts[index % demoProducts.length] || 'Demo Product',
            commissionRate: parseFloat(node.commissionRate) * 100, // Convert to percentage
            commissionType: 'percentage',
            originalPrice,
            currentPrice,
            discount: discountPercent,
            imageUrl: '/placeholder-image.svg',
            productUrl: 'https://shopee.co.th',
            shopName: `Shop ${index + 1}`,
            rating: Math.round((Math.random() * 2 + 3) * 10) / 10, // 3.0 - 5.0
            sold: Math.floor(Math.random() * 10000) + 100,
            categoryId: Math.floor(Math.random() * 10) + 1,
            categoryName: ['Electronics', 'Fashion', 'Home & Living', 'Sports', 'Beauty'][Math.floor(Math.random() * 5)],
            brand: ['Apple', 'Samsung', 'Sony', 'Canon', 'Nintendo'][Math.floor(Math.random() * 5)],
            description: `High-quality ${demoProducts[index % demoProducts.length]} with excellent features and performance.`
          };
        }) || [],
        totalCount: result.data?.productOfferV2?.nodes?.length || 0,
        scrollId: undefined
      }
    };

    return NextResponse.json(transformedData);
  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
