"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ProductCard(param) {\n    let { product, onGetShortLink } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleGetShortLink = async ()=>{\n        if (!onGetShortLink) return;\n        setIsLoading(true);\n        try {\n            await onGetShortLink(product.productUrl);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('th-TH', {\n            style: 'currency',\n            currency: 'THB'\n        }).format(price);\n    };\n    const formatRating = (rating)=>{\n        return rating.toFixed(1);\n    };\n    const formatSold = (sold)=>{\n        if (sold >= 1000) {\n            return \"\".concat((sold / 1000).toFixed(1), \"k\");\n        }\n        return sold.toString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Image, {\n                        src: product.imageUrl,\n                        alt: product.offerName,\n                        className: \"w-full h-full object-cover\",\n                        onError: (e)=>{\n                            const target = e.target;\n                            target.src = '/placeholder-image.svg';\n                        }\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 47,\n                        columnNumber: 9\n                    }, this),\n                    product.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold\",\n                        children: [\n                            \"-\",\n                            product.discount,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-sm font-semibold\",\n                        children: [\n                            product.commissionRate,\n                            \"% คอมมิชชั่น\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 61,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-800 mb-2 line-clamp-2 text-sm\",\n                        children: product.offerName\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 68,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-xs mb-2\",\n                        children: product.shopName\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 73,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600 font-bold text-lg\",\n                                children: formatPrice(product.currentPrice)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 77,\n                                columnNumber: 11\n                            }, this),\n                            product.originalPrice > product.currentPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 line-through text-sm\",\n                                children: formatPrice(product.originalPrice)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 81,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 76,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-sm text-gray-600 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-500\",\n                                        children: \"★\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                        lineNumber: 90,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: formatRating(product.rating)\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                        lineNumber: 91,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 89,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    formatSold(product.sold),\n                                    \" ขายแล้ว\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 93,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 88,\n                        columnNumber: 9\n                    }, this),\n                    product.categoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs\",\n                            children: product.categoryName\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                            lineNumber: 99,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: product.productUrl,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex-1 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded text-sm font-medium text-center transition-colors\",\n                                children: \"ดูสินค้า\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            onGetShortLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGetShortLink,\n                                disabled: isLoading,\n                                className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-2 px-4 rounded text-sm font-medium transition-colors\",\n                                children: isLoading ? '...' : 'ลิงก์สั้น'\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 116,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                lineNumber: 67,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n        lineNumber: 44,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"EmvgwIb3cHpoFpeP+WmEDbjx4y4=\");\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCard.tsx\n"));

/***/ })

});