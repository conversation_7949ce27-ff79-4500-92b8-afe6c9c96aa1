'use client';

import { OfferNode } from '@/lib/types';
import Image from 'next/image';
import { useState } from 'react';

interface ProductCardProps {
  product: OfferNode;
  onGetShortLink?: (productUrl: string) => void;
}

export default function ProductCard({ product, onGetShortLink }: ProductCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleGetShortLink = async () => {
    if (!onGetShortLink) return;
    
    setIsLoading(true);
    try {
      await onGetShortLink(product.productUrl);
    } finally {
      setIsLoading(false);
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
    }).format(price);
  };

  const formatRating = (rating: number) => {
    return rating.toFixed(1);
  };

  const formatSold = (sold: number) => {
    if (sold >= 1000) {
      return `${(sold / 1000).toFixed(1)}k`;
    }
    return sold.toString();
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {/* Product Image */}
      <div className="relative aspect-square">
        <Image
          src={product.imageUrl}
          alt={product.offerName}
          className="w-full h-full object-cover"
          onError={(e) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-image.svg';
          }}
        />
        {product.discount > 0 && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">
            -{product.discount}%
          </div>
        )}
        <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-sm font-semibold">
          {product.commissionRate}% คอมมิชชั่น
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2 text-sm">
          {product.offerName}
        </h3>

        {/* Shop Name */}
        <p className="text-gray-600 text-xs mb-2">{product.shopName}</p>

        {/* Price */}
        <div className="flex items-center gap-2 mb-2">
          <span className="text-orange-600 font-bold text-lg">
            {formatPrice(product.currentPrice)}
          </span>
          {product.originalPrice > product.currentPrice && (
            <span className="text-gray-400 line-through text-sm">
              {formatPrice(product.originalPrice)}
            </span>
          )}
        </div>

        {/* Rating and Sold */}
        <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
          <div className="flex items-center gap-1">
            <span className="text-yellow-500">★</span>
            <span>{formatRating(product.rating)}</span>
          </div>
          <span>{formatSold(product.sold)} ขายแล้ว</span>
        </div>

        {/* Category */}
        {product.categoryName && (
          <div className="mb-3">
            <span className="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
              {product.categoryName}
            </span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <a
            href={product.productUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded text-sm font-medium text-center transition-colors"
          >
            ดูสินค้า
          </a>
          {onGetShortLink && (
            <button
              onClick={handleGetShortLink}
              disabled={isLoading}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-2 px-4 rounded text-sm font-medium transition-colors"
            >
              {isLoading ? '...' : 'ลิงก์สั้น'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
