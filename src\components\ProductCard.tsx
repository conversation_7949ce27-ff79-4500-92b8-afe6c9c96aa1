'use client';

import { OfferNode } from '@/lib/types';
import { useState } from 'react';

interface ProductCardProps {
  product: OfferNode;
  onGetShortLink?: (productUrl: string) => void;
}

export default function ProductCard({ product, onGetShortLink }: ProductCardProps) {
  const [isLoading, setIsLoading] = useState(false);

  const handleGetShortLink = async () => {
    if (!onGetShortLink) return;

    setIsLoading(true);
    try {
      await onGetShortLink(product.productUrl);
    } finally {
      setIsLoading(false);
    }
  };

  // Generate product image based on product name
  const getProductImage = (productName: string) => {
    const keywords = productName.toLowerCase();

    if (keywords.includes('iphone') || keywords.includes('apple watch')) {
      return 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('samsung') || keywords.includes('galaxy')) {
      return 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('macbook') || keywords.includes('laptop') || keywords.includes('dell') || keywords.includes('xps')) {
      return 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('ipad') || keywords.includes('tablet')) {
      return 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('airpods') || keywords.includes('earbuds') || keywords.includes('bose')) {
      return 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('sony') || keywords.includes('wh-1000') || keywords.includes('headphone')) {
      return 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('nintendo') || keywords.includes('switch')) {
      return 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('playstation') || keywords.includes('ps5')) {
      return 'https://images.unsplash.com/photo-1607853202273-797f1c22a38e?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('canon') || keywords.includes('camera')) {
      return 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('gopro') || keywords.includes('hero')) {
      return 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('dyson') || keywords.includes('vacuum')) {
      return 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('kitchenaid') || keywords.includes('mixer')) {
      return 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('instant pot') || keywords.includes('pressure cooker')) {
      return 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('fitbit') || keywords.includes('fitness')) {
      return 'https://images.unsplash.com/photo-1575311373937-040b8e1fd5b6?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('ring') || keywords.includes('doorbell')) {
      return 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('echo') || keywords.includes('alexa')) {
      return 'https://images.unsplash.com/photo-1543512214-318c7553f230?w=400&h=400&fit=crop&crop=center';
    } else if (keywords.includes('kindle') || keywords.includes('ebook')) {
      return 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop&crop=center';
    } else {
      // Default tech product image
      return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=center';
    }
  };

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('th-TH', {
      style: 'currency',
      currency: 'THB',
    }).format(price);
  };

  const formatRating = (rating: number) => {
    return rating.toFixed(1);
  };

  const formatSold = (sold: number) => {
    if (sold >= 1000) {
      return `${(sold / 1000).toFixed(1)}k`;
    }
    return sold.toString();
  };

  return (
    <div className="bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300">
      {/* Product Image */}
      <div className="relative aspect-square bg-gray-100">
        <img
          src={getProductImage(product.offerName)}
          alt={product.offerName}
          className="w-full h-full object-cover rounded-t-lg"
          onError={(e: React.SyntheticEvent<HTMLImageElement, Event>) => {
            const target = e.target as HTMLImageElement;
            target.src = '/placeholder-image.svg';
          }}
        />
        {product.discount > 0 && (
          <div className="absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold">
            -{product.discount}%
          </div>
        )}
        <div className="absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-sm font-semibold">
          {product.commissionRate.toFixed(1)}% คอมมิชชั่น
        </div>
      </div>

      {/* Product Info */}
      <div className="p-4">
        <h3 className="font-semibold text-gray-800 mb-2 line-clamp-2 text-sm">
          {product.offerName}
        </h3>

        {/* Shop Name */}
        <p className="text-gray-600 text-xs mb-2">{product.shopName}</p>

        {/* Price */}
        <div className="flex items-center gap-2 mb-2">
          <span className="text-orange-600 font-bold text-lg">
            {formatPrice(product.currentPrice)}
          </span>
          {product.originalPrice > product.currentPrice && (
            <span className="text-gray-400 line-through text-sm">
              {formatPrice(product.originalPrice)}
            </span>
          )}
        </div>

        {/* Rating and Sold */}
        <div className="flex items-center justify-between text-sm text-gray-600 mb-3">
          <div className="flex items-center gap-1">
            <span className="text-yellow-500">★</span>
            <span>{formatRating(product.rating)}</span>
          </div>
          <span>{formatSold(product.sold)} ขายแล้ว</span>
        </div>

        {/* Category */}
        {product.categoryName && (
          <div className="mb-3">
            <span className="inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
              {product.categoryName}
            </span>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex gap-2">
          <a
            href={product.productUrl}
            target="_blank"
            rel="noopener noreferrer"
            className="flex-1 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded text-sm font-medium text-center transition-colors"
          >
            ดูสินค้า
          </a>
          {onGetShortLink && (
            <button
              onClick={handleGetShortLink}
              disabled={isLoading}
              className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-2 px-4 rounded text-sm font-medium transition-colors"
            >
              {isLoading ? '...' : 'ลิงก์สั้น'}
            </button>
          )}
        </div>
      </div>
    </div>
  );
}
