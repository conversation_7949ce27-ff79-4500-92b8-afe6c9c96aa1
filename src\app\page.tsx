'use client';

import { useState, useEffect } from 'react';
import SearchBar from '@/components/SearchBar';
import ProductCard from '@/components/ProductCard';
import Loading, { ProductGridSkeleton } from '@/components/Loading';
import { OfferNode, SearchParams } from '@/lib/types';

export default function Home() {
  const [products, setProducts] = useState<OfferNode[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [scrollId, setScrollId] = useState<string | undefined>();
  const [hasMore, setHasMore] = useState(true);
  const [totalCount, setTotalCount] = useState(0);

  // Load initial products
  useEffect(() => {
    handleSearch({ limit: 20 });
  }, []);

  const handleSearch = async (params: SearchParams) => {
    setIsLoading(true);
    setError(null);

    try {
      const queryParams = new URLSearchParams();

      if (params.keyword) queryParams.append('keyword', params.keyword);
      if (params.categoryId) queryParams.append('categoryId', params.categoryId.toString());
      if (params.minPrice) queryParams.append('minPrice', params.minPrice.toString());
      if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice.toString());
      if (params.sortBy) queryParams.append('sortBy', params.sortBy);
      if (params.limit) queryParams.append('limit', params.limit.toString());
      if (params.scrollId) queryParams.append('scrollId', params.scrollId);

      const response = await fetch(`/api/shopee/offers?${queryParams.toString()}`);

      if (!response.ok) {
        throw new Error('Failed to fetch products');
      }

      const data = await response.json();

      if (data.offerList) {
        if (params.scrollId) {
          // Append to existing products for pagination
          setProducts(prev => [...prev, ...data.offerList.nodes]);
        } else {
          // Replace products for new search
          setProducts(data.offerList.nodes);
        }

        setScrollId(data.offerList.scrollId);
        setTotalCount(data.offerList.totalCount);
        setHasMore(data.offerList.nodes.length === (params.limit || 20));
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'An error occurred');
      console.error('Search error:', err);
    } finally {
      setIsLoading(false);
    }
  };

  const handleLoadMore = () => {
    if (scrollId && hasMore && !isLoading) {
      handleSearch({ scrollId, limit: 20 });
    }
  };

  const handleGetShortLink = async (productUrl: string) => {
    try {
      const response = await fetch('/api/shopee/short-link', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ originalLink: productUrl }),
      });

      if (!response.ok) {
        throw new Error('Failed to generate short link');
      }

      const data = await response.json();

      if (data.shortLink) {
        // Copy to clipboard
        await navigator.clipboard.writeText(data.shortLink.shortLink);
        alert('ลิงก์สั้นถูกคัดลอกไปยังคลิปบอร์ดแล้ว!');
      }
    } catch (err) {
      console.error('Short link error:', err);
      alert('ไม่สามารถสร้างลิงก์สั้นได้');
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">
                🛍️ Shopee Product Recommendations
              </h1>
              <p className="text-gray-600 mt-2">
                ค้นหาและแนะนำสินค้าจาก Shopee พร้อมคอมมิชชั่น
              </p>
            </div>
            <nav className="hidden md:flex space-x-4">
              <a
                href="/"
                className="text-gray-700 hover:text-orange-600 font-medium transition-colors"
              >
                หน้าหลัก
              </a>
              <a
                href="/about"
                className="text-gray-700 hover:text-orange-600 font-medium transition-colors"
              >
                เกี่ยวกับเรา
              </a>
            </nav>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Search Bar */}
        <SearchBar onSearch={handleSearch} isLoading={isLoading} />

        {/* Results Summary */}
        {totalCount > 0 && (
          <div className="mb-6">
            <p className="text-gray-600">
              พบสินค้า {totalCount.toLocaleString()} รายการ
              {products.length < totalCount && ` (แสดง ${products.length} รายการ)`}
            </p>
          </div>
        )}

        {/* Error Message */}
        {error && (
          <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
            <p>เกิดข้อผิดพลาด: {error}</p>
          </div>
        )}

        {/* Products Grid */}
        {isLoading && products.length === 0 ? (
          <ProductGridSkeleton />
        ) : (
          <>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8">
              {products.map((product) => (
                <ProductCard
                  key={product.offerId}
                  product={product}
                  onGetShortLink={handleGetShortLink}
                />
              ))}
            </div>

            {/* Load More Button */}
            {hasMore && products.length > 0 && (
              <div className="text-center">
                <button
                  onClick={handleLoadMore}
                  disabled={isLoading}
                  className="bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white px-8 py-3 rounded-lg font-medium transition-colors"
                >
                  {isLoading ? 'กำลังโหลด...' : 'โหลดเพิ่มเติม'}
                </button>
              </div>
            )}

            {/* Loading More */}
            {isLoading && products.length > 0 && (
              <Loading />
            )}
          </>
        )}

        {/* No Results */}
        {!isLoading && products.length === 0 && !error && (
          <div className="text-center py-12">
            <p className="text-gray-500 text-lg">ไม่พบสินค้าที่ตรงกับการค้นหา</p>
            <p className="text-gray-400 mt-2">ลองเปลี่ยนคำค้นหาหรือปรับตัวกรองใหม่</p>
          </div>
        )}
      </main>

      {/* Footer */}
      <footer className="bg-white border-t mt-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center text-gray-600">
            <p>© 2024 Shopee Product Recommendations</p>
            <p className="mt-2 text-sm">
              Powered by Shopee Affiliate API
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
