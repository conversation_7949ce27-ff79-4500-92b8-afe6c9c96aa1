export default function About() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <h1 className="text-3xl font-bold text-gray-900">
            📖 เกี่ยวกับเรา
          </h1>
          <p className="text-gray-600 mt-2">
            ข้อมูลเกี่ยวกับเว็บไซต์แนะนำสินค้า Shopee
          </p>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="bg-white rounded-lg shadow-md p-8 mb-8">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            🛍️ Shopee Product Recommendations
          </h2>
          
          <div className="prose max-w-none">
            <p className="text-gray-700 mb-4">
              เว็บไซต์นี้เป็นแพลตฟอร์มสำหรับแนะนำสินค้าจาก Shopee โดยใช้ Shopee Affiliate API 
              เพื่อให้ผู้ใช้สามารถค้นหาสินค้าที่ต้องการได้อย่างง่ายดาย พร้อมทั้งแสดงข้อมูลคอมมิชชั่น
              และรายละเอียดสินค้าที่ครบถ้วน
            </p>

            <h3 className="text-xl font-semibold text-gray-900 mb-3">✨ คุณสมบัติหลัก</h3>
            <ul className="list-disc list-inside text-gray-700 mb-6 space-y-2">
              <li>🔍 ค้นหาสินค้าด้วยคำค้นหา</li>
              <li>💰 แสดงอัตราคอมมิชชั่นของแต่ละสินค้า</li>
              <li>🏷️ แสดงราคาเดิม ราคาปัจจุบัน และส่วนลด</li>
              <li>⭐ แสดงคะแนนรีวิวและยอดขาย</li>
              <li>🔗 สร้างลิงก์สั้นสำหรับแชร์</li>
              <li>📱 รองรับทุกขนาดหน้าจอ (Responsive Design)</li>
              <li>🎨 UI/UX ที่ทันสมัยด้วย Tailwind CSS</li>
            </ul>

            <h3 className="text-xl font-semibold text-gray-900 mb-3">🛠️ เทคโนโลยีที่ใช้</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Frontend</h4>
                <ul className="text-gray-700 space-y-1">
                  <li>• Next.js 15 with App Router</li>
                  <li>• TypeScript</li>
                  <li>• Tailwind CSS</li>
                  <li>• React Hooks</li>
                </ul>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h4 className="font-semibold text-gray-900 mb-2">Backend & API</h4>
                <ul className="text-gray-700 space-y-1">
                  <li>• Shopee Affiliate GraphQL API</li>
                  <li>• Next.js API Routes</li>
                  <li>• SHA256 Authentication</li>
                  <li>• Unsplash Images API</li>
                </ul>
              </div>
            </div>

            <h3 className="text-xl font-semibold text-gray-900 mb-3">🔐 ความปลอดภัย</h3>
            <p className="text-gray-700 mb-4">
              เว็บไซต์ใช้ระบบ authentication ที่ปลอดภัยด้วย SHA256 signature สำหรับการเชื่อมต่อกับ 
              Shopee Affiliate API และไม่เก็บข้อมูลส่วนตัวของผู้ใช้
            </p>

            <h3 className="text-xl font-semibold text-gray-900 mb-3">📞 ติดต่อเรา</h3>
            <p className="text-gray-700 mb-2">
              หากมีคำถามหรือข้อเสนอแนะ สามารถติดต่อเราได้ที่:
            </p>
            <ul className="text-gray-700 space-y-1">
              <li>📧 Email: <EMAIL></li>
              <li>🐙 GitHub: github.com/your-username/shopeeproduct</li>
              <li>📱 Line: @shopeeproduct</li>
            </ul>
          </div>
        </div>

        {/* Back to Home */}
        <div className="text-center">
          <a
            href="/"
            className="inline-flex items-center px-6 py-3 bg-orange-500 hover:bg-orange-600 text-white font-medium rounded-lg transition-colors"
          >
            ← กลับหน้าหลัก
          </a>
        </div>
      </main>
    </div>
  );
}
