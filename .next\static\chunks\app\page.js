/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/page"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CPROJECT%5C%5Cshopeeproduct%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!*************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CPROJECT%5C%5Cshopeeproduct%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \*************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(app-pages-browser)/./src/app/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvYnVpbGQvd2VicGFjay9sb2FkZXJzL25leHQtZmxpZ2h0LWNsaWVudC1lbnRyeS1sb2FkZXIuanM/bW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIySCUzQSU1QyU1Q1BST0pFQ1QlNUMlNUNzaG9wZWVwcm9kdWN0JTVDJTVDc3JjJTVDJTVDYXBwJTVDJTVDcGFnZS50c3glMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPWZhbHNlISIsIm1hcHBpbmdzIjoiQUFBQSw4SkFBbUYiLCJzb3VyY2VzIjpbIiJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkg6XFxcXFBST0pFQ1RcXFxcc2hvcGVlcHJvZHVjdFxcXFxzcmNcXFxcYXBwXFxcXHBhZ2UudHN4XCIpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CPROJECT%5C%5Cshopeeproduct%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n true &&\n  (function () {\n    function getComponentNameFromType(type) {\n      if (null == type) return null;\n      if (\"function\" === typeof type)\n        return type.$$typeof === REACT_CLIENT_REFERENCE\n          ? null\n          : type.displayName || type.name || null;\n      if (\"string\" === typeof type) return type;\n      switch (type) {\n        case REACT_FRAGMENT_TYPE:\n          return \"Fragment\";\n        case REACT_PROFILER_TYPE:\n          return \"Profiler\";\n        case REACT_STRICT_MODE_TYPE:\n          return \"StrictMode\";\n        case REACT_SUSPENSE_TYPE:\n          return \"Suspense\";\n        case REACT_SUSPENSE_LIST_TYPE:\n          return \"SuspenseList\";\n        case REACT_ACTIVITY_TYPE:\n          return \"Activity\";\n      }\n      if (\"object\" === typeof type)\n        switch (\n          (\"number\" === typeof type.tag &&\n            console.error(\n              \"Received an unexpected object in getComponentNameFromType(). This is likely a bug in React. Please file an issue.\"\n            ),\n          type.$$typeof)\n        ) {\n          case REACT_PORTAL_TYPE:\n            return \"Portal\";\n          case REACT_CONTEXT_TYPE:\n            return (type.displayName || \"Context\") + \".Provider\";\n          case REACT_CONSUMER_TYPE:\n            return (type._context.displayName || \"Context\") + \".Consumer\";\n          case REACT_FORWARD_REF_TYPE:\n            var innerType = type.render;\n            type = type.displayName;\n            type ||\n              ((type = innerType.displayName || innerType.name || \"\"),\n              (type = \"\" !== type ? \"ForwardRef(\" + type + \")\" : \"ForwardRef\"));\n            return type;\n          case REACT_MEMO_TYPE:\n            return (\n              (innerType = type.displayName || null),\n              null !== innerType\n                ? innerType\n                : getComponentNameFromType(type.type) || \"Memo\"\n            );\n          case REACT_LAZY_TYPE:\n            innerType = type._payload;\n            type = type._init;\n            try {\n              return getComponentNameFromType(type(innerType));\n            } catch (x) {}\n        }\n      return null;\n    }\n    function testStringCoercion(value) {\n      return \"\" + value;\n    }\n    function checkKeyStringCoercion(value) {\n      try {\n        testStringCoercion(value);\n        var JSCompiler_inline_result = !1;\n      } catch (e) {\n        JSCompiler_inline_result = !0;\n      }\n      if (JSCompiler_inline_result) {\n        JSCompiler_inline_result = console;\n        var JSCompiler_temp_const = JSCompiler_inline_result.error;\n        var JSCompiler_inline_result$jscomp$0 =\n          (\"function\" === typeof Symbol &&\n            Symbol.toStringTag &&\n            value[Symbol.toStringTag]) ||\n          value.constructor.name ||\n          \"Object\";\n        JSCompiler_temp_const.call(\n          JSCompiler_inline_result,\n          \"The provided key is an unsupported type %s. This value must be coerced to a string before using it here.\",\n          JSCompiler_inline_result$jscomp$0\n        );\n        return testStringCoercion(value);\n      }\n    }\n    function getTaskName(type) {\n      if (type === REACT_FRAGMENT_TYPE) return \"<>\";\n      if (\n        \"object\" === typeof type &&\n        null !== type &&\n        type.$$typeof === REACT_LAZY_TYPE\n      )\n        return \"<...>\";\n      try {\n        var name = getComponentNameFromType(type);\n        return name ? \"<\" + name + \">\" : \"<...>\";\n      } catch (x) {\n        return \"<...>\";\n      }\n    }\n    function getOwner() {\n      var dispatcher = ReactSharedInternals.A;\n      return null === dispatcher ? null : dispatcher.getOwner();\n    }\n    function UnknownOwner() {\n      return Error(\"react-stack-top-frame\");\n    }\n    function hasValidKey(config) {\n      if (hasOwnProperty.call(config, \"key\")) {\n        var getter = Object.getOwnPropertyDescriptor(config, \"key\").get;\n        if (getter && getter.isReactWarning) return !1;\n      }\n      return void 0 !== config.key;\n    }\n    function defineKeyPropWarningGetter(props, displayName) {\n      function warnAboutAccessingKey() {\n        specialPropKeyWarningShown ||\n          ((specialPropKeyWarningShown = !0),\n          console.error(\n            \"%s: `key` is not a prop. Trying to access it will result in `undefined` being returned. If you need to access the same value within the child component, you should pass it as a different prop. (https://react.dev/link/special-props)\",\n            displayName\n          ));\n      }\n      warnAboutAccessingKey.isReactWarning = !0;\n      Object.defineProperty(props, \"key\", {\n        get: warnAboutAccessingKey,\n        configurable: !0\n      });\n    }\n    function elementRefGetterWithDeprecationWarning() {\n      var componentName = getComponentNameFromType(this.type);\n      didWarnAboutElementRef[componentName] ||\n        ((didWarnAboutElementRef[componentName] = !0),\n        console.error(\n          \"Accessing element.ref was removed in React 19. ref is now a regular prop. It will be removed from the JSX Element type in a future release.\"\n        ));\n      componentName = this.props.ref;\n      return void 0 !== componentName ? componentName : null;\n    }\n    function ReactElement(\n      type,\n      key,\n      self,\n      source,\n      owner,\n      props,\n      debugStack,\n      debugTask\n    ) {\n      self = props.ref;\n      type = {\n        $$typeof: REACT_ELEMENT_TYPE,\n        type: type,\n        key: key,\n        props: props,\n        _owner: owner\n      };\n      null !== (void 0 !== self ? self : null)\n        ? Object.defineProperty(type, \"ref\", {\n            enumerable: !1,\n            get: elementRefGetterWithDeprecationWarning\n          })\n        : Object.defineProperty(type, \"ref\", { enumerable: !1, value: null });\n      type._store = {};\n      Object.defineProperty(type._store, \"validated\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: 0\n      });\n      Object.defineProperty(type, \"_debugInfo\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: null\n      });\n      Object.defineProperty(type, \"_debugStack\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugStack\n      });\n      Object.defineProperty(type, \"_debugTask\", {\n        configurable: !1,\n        enumerable: !1,\n        writable: !0,\n        value: debugTask\n      });\n      Object.freeze && (Object.freeze(type.props), Object.freeze(type));\n      return type;\n    }\n    function jsxDEVImpl(\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self,\n      debugStack,\n      debugTask\n    ) {\n      var children = config.children;\n      if (void 0 !== children)\n        if (isStaticChildren)\n          if (isArrayImpl(children)) {\n            for (\n              isStaticChildren = 0;\n              isStaticChildren < children.length;\n              isStaticChildren++\n            )\n              validateChildKeys(children[isStaticChildren]);\n            Object.freeze && Object.freeze(children);\n          } else\n            console.error(\n              \"React.jsx: Static children should always be an array. You are likely explicitly calling React.jsxs or React.jsxDEV. Use the Babel transform instead.\"\n            );\n        else validateChildKeys(children);\n      if (hasOwnProperty.call(config, \"key\")) {\n        children = getComponentNameFromType(type);\n        var keys = Object.keys(config).filter(function (k) {\n          return \"key\" !== k;\n        });\n        isStaticChildren =\n          0 < keys.length\n            ? \"{key: someKey, \" + keys.join(\": ..., \") + \": ...}\"\n            : \"{key: someKey}\";\n        didWarnAboutKeySpread[children + isStaticChildren] ||\n          ((keys =\n            0 < keys.length ? \"{\" + keys.join(\": ..., \") + \": ...}\" : \"{}\"),\n          console.error(\n            'A props object containing a \"key\" prop is being spread into JSX:\\n  let props = %s;\\n  <%s {...props} />\\nReact keys must be passed directly to JSX without using spread:\\n  let props = %s;\\n  <%s key={someKey} {...props} />',\n            isStaticChildren,\n            children,\n            keys,\n            children\n          ),\n          (didWarnAboutKeySpread[children + isStaticChildren] = !0));\n      }\n      children = null;\n      void 0 !== maybeKey &&\n        (checkKeyStringCoercion(maybeKey), (children = \"\" + maybeKey));\n      hasValidKey(config) &&\n        (checkKeyStringCoercion(config.key), (children = \"\" + config.key));\n      if (\"key\" in config) {\n        maybeKey = {};\n        for (var propName in config)\n          \"key\" !== propName && (maybeKey[propName] = config[propName]);\n      } else maybeKey = config;\n      children &&\n        defineKeyPropWarningGetter(\n          maybeKey,\n          \"function\" === typeof type\n            ? type.displayName || type.name || \"Unknown\"\n            : type\n        );\n      return ReactElement(\n        type,\n        children,\n        self,\n        source,\n        getOwner(),\n        maybeKey,\n        debugStack,\n        debugTask\n      );\n    }\n    function validateChildKeys(node) {\n      \"object\" === typeof node &&\n        null !== node &&\n        node.$$typeof === REACT_ELEMENT_TYPE &&\n        node._store &&\n        (node._store.validated = 1);\n    }\n    var React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\"),\n      REACT_ELEMENT_TYPE = Symbol.for(\"react.transitional.element\"),\n      REACT_PORTAL_TYPE = Symbol.for(\"react.portal\"),\n      REACT_FRAGMENT_TYPE = Symbol.for(\"react.fragment\"),\n      REACT_STRICT_MODE_TYPE = Symbol.for(\"react.strict_mode\"),\n      REACT_PROFILER_TYPE = Symbol.for(\"react.profiler\");\n    Symbol.for(\"react.provider\");\n    var REACT_CONSUMER_TYPE = Symbol.for(\"react.consumer\"),\n      REACT_CONTEXT_TYPE = Symbol.for(\"react.context\"),\n      REACT_FORWARD_REF_TYPE = Symbol.for(\"react.forward_ref\"),\n      REACT_SUSPENSE_TYPE = Symbol.for(\"react.suspense\"),\n      REACT_SUSPENSE_LIST_TYPE = Symbol.for(\"react.suspense_list\"),\n      REACT_MEMO_TYPE = Symbol.for(\"react.memo\"),\n      REACT_LAZY_TYPE = Symbol.for(\"react.lazy\"),\n      REACT_ACTIVITY_TYPE = Symbol.for(\"react.activity\"),\n      REACT_CLIENT_REFERENCE = Symbol.for(\"react.client.reference\"),\n      ReactSharedInternals =\n        React.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,\n      hasOwnProperty = Object.prototype.hasOwnProperty,\n      isArrayImpl = Array.isArray,\n      createTask = console.createTask\n        ? console.createTask\n        : function () {\n            return null;\n          };\n    React = {\n      \"react-stack-bottom-frame\": function (callStackForError) {\n        return callStackForError();\n      }\n    };\n    var specialPropKeyWarningShown;\n    var didWarnAboutElementRef = {};\n    var unknownOwnerDebugStack = React[\"react-stack-bottom-frame\"].bind(\n      React,\n      UnknownOwner\n    )();\n    var unknownOwnerDebugTask = createTask(getTaskName(UnknownOwner));\n    var didWarnAboutKeySpread = {};\n    exports.Fragment = REACT_FRAGMENT_TYPE;\n    exports.jsxDEV = function (\n      type,\n      config,\n      maybeKey,\n      isStaticChildren,\n      source,\n      self\n    ) {\n      var trackActualOwner =\n        1e4 > ReactSharedInternals.recentlyCreatedOwnerStacks++;\n      return jsxDEVImpl(\n        type,\n        config,\n        maybeKey,\n        isStaticChildren,\n        source,\n        self,\n        trackActualOwner\n          ? Error(\"react-stack-top-frame\")\n          : unknownOwnerDebugStack,\n        trackActualOwner ? createTask(getTaskName(type)) : unknownOwnerDebugTask\n      );\n    };\n  })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIkg6XFxQUk9KRUNUXFxzaG9wZWVwcm9kdWN0XFxub2RlX21vZHVsZXNcXG5leHRcXGRpc3RcXGNvbXBpbGVkXFxyZWFjdFxcanN4LWRldi1ydW50aW1lLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIid1c2Ugc3RyaWN0JztcblxuaWYgKHByb2Nlc3MuZW52Lk5PREVfRU5WID09PSAncHJvZHVjdGlvbicpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUucHJvZHVjdGlvbi5qcycpO1xufSBlbHNlIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSByZXF1aXJlKCcuL2Nqcy9yZWFjdC1qc3gtZGV2LXJ1bnRpbWUuZGV2ZWxvcG1lbnQuanMnKTtcbn1cbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_SearchBar__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/SearchBar */ \"(app-pages-browser)/./src/components/SearchBar.tsx\");\n/* harmony import */ var _components_ProductCard__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ProductCard */ \"(app-pages-browser)/./src/components/ProductCard.tsx\");\n/* harmony import */ var _components_Loading__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/Loading */ \"(app-pages-browser)/./src/components/Loading.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\nfunction Home() {\n    _s();\n    const [products, setProducts] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [scrollId, setScrollId] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)();\n    const [hasMore, setHasMore] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [totalCount, setTotalCount] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    // Load initial products\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"Home.useEffect\": ()=>{\n            handleSearch({\n                limit: 20\n            });\n        }\n    }[\"Home.useEffect\"], []);\n    const handleSearch = async (params)=>{\n        setIsLoading(true);\n        setError(null);\n        try {\n            const queryParams = new URLSearchParams();\n            if (params.keyword) queryParams.append('keyword', params.keyword);\n            if (params.categoryId) queryParams.append('categoryId', params.categoryId.toString());\n            if (params.minPrice) queryParams.append('minPrice', params.minPrice.toString());\n            if (params.maxPrice) queryParams.append('maxPrice', params.maxPrice.toString());\n            if (params.sortBy) queryParams.append('sortBy', params.sortBy);\n            if (params.limit) queryParams.append('limit', params.limit.toString());\n            if (params.scrollId) queryParams.append('scrollId', params.scrollId);\n            const response = await fetch(\"/api/shopee/offers?\".concat(queryParams.toString()));\n            if (!response.ok) {\n                throw new Error('Failed to fetch products');\n            }\n            const data = await response.json();\n            if (data.offerList) {\n                if (params.scrollId) {\n                    // Append to existing products for pagination\n                    setProducts((prev)=>[\n                            ...prev,\n                            ...data.offerList.nodes\n                        ]);\n                } else {\n                    // Replace products for new search\n                    setProducts(data.offerList.nodes);\n                }\n                setScrollId(data.offerList.scrollId);\n                setTotalCount(data.offerList.totalCount);\n                setHasMore(data.offerList.nodes.length === (params.limit || 20));\n            }\n        } catch (err) {\n            setError(err instanceof Error ? err.message : 'An error occurred');\n            console.error('Search error:', err);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleLoadMore = ()=>{\n        if (scrollId && hasMore && !isLoading) {\n            handleSearch({\n                scrollId,\n                limit: 20\n            });\n        }\n    };\n    const handleGetShortLink = async (productUrl)=>{\n        try {\n            const response = await fetch('/api/shopee/short-link', {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json'\n                },\n                body: JSON.stringify({\n                    originalLink: productUrl\n                })\n            });\n            if (!response.ok) {\n                throw new Error('Failed to generate short link');\n            }\n            const data = await response.json();\n            if (data.shortLink) {\n                // Copy to clipboard\n                await navigator.clipboard.writeText(data.shortLink.shortLink);\n                alert('ลิงก์สั้นถูกคัดลอกไปยังคลิปบอร์ดแล้ว!');\n            }\n        } catch (err) {\n            console.error('Short link error:', err);\n            alert('ไม่สามารถสร้างลิงก์สั้นได้');\n        }\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gray-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"bg-white shadow-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"text-3xl font-bold text-gray-900\",\n                                        children: \"\\uD83D\\uDECD️ Shopee Product Recommendations\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 106,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-600 mt-2\",\n                                        children: \"ค้นหาและแนะนำสินค้าจาก Shopee พร้อมคอมมิชชั่น\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 109,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 105,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                                className: \"hidden md:flex space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/\",\n                                        className: \"text-gray-700 hover:text-orange-600 font-medium transition-colors\",\n                                        children: \"หน้าหลัก\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 114,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                        href: \"/about\",\n                                        className: \"text-gray-700 hover:text-orange-600 font-medium transition-colors\",\n                                        children: \"เกี่ยวกับเรา\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 113,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 104,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 103,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 102,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_SearchBar__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                        onSearch: handleSearch,\n                        isLoading: isLoading\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 134,\n                        columnNumber: 9\n                    }, this),\n                    totalCount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-600\",\n                            children: [\n                                \"พบสินค้า \",\n                                totalCount.toLocaleString(),\n                                \" รายการ\",\n                                products.length < totalCount && \" (แสดง \".concat(products.length, \" รายการ)\")\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 139,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 138,\n                        columnNumber: 11\n                    }, this),\n                    error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            children: [\n                                \"เกิดข้อผิดพลาด: \",\n                                error\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 149,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 148,\n                        columnNumber: 11\n                    }, this),\n                    isLoading && products.length === 0 ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loading__WEBPACK_IMPORTED_MODULE_4__.ProductGridSkeleton, {}, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 155,\n                        columnNumber: 11\n                    }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6 mb-8\",\n                                children: products.map((product)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ProductCard__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n                                        product: product,\n                                        onGetShortLink: handleGetShortLink\n                                    }, product.offerId, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 160,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 158,\n                                columnNumber: 13\n                            }, this),\n                            hasMore && products.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleLoadMore,\n                                    disabled: isLoading,\n                                    className: \"bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white px-8 py-3 rounded-lg font-medium transition-colors\",\n                                    children: isLoading ? 'กำลังโหลด...' : 'โหลดเพิ่มเติม'\n                                }, void 0, false, {\n                                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 171,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 170,\n                                columnNumber: 15\n                            }, this),\n                            isLoading && products.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_Loading__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {}, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 183,\n                                columnNumber: 15\n                            }, this)\n                        ]\n                    }, void 0, true),\n                    !isLoading && products.length === 0 && !error && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-12\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500 text-lg\",\n                                children: \"ไม่พบสินค้าที่ตรงกับการค้นหา\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 191,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-400 mt-2\",\n                                children: \"ลองเปลี่ยนคำค้นหาหรือปรับตัวกรองใหม่\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 192,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 190,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 132,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-white border-t mt-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center text-gray-600\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 Shopee Product Recommendations\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"mt-2 text-sm\",\n                                children: \"Powered by Shopee Affiliate API\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 200,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 100,\n        columnNumber: 5\n    }, this);\n}\n_s(Home, \"8SP/4DRBkz22iR4kGz9adc6u0jY=\");\n_c = Home;\nvar _c;\n$RefreshReg$(_c, \"Home\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/app/page.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/Loading.tsx":
/*!************************************!*\
  !*** ./src/components/Loading.tsx ***!
  \************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ProductCardSkeleton: () => (/* binding */ ProductCardSkeleton),\n/* harmony export */   ProductGridSkeleton: () => (/* binding */ ProductGridSkeleton),\n/* harmony export */   \"default\": () => (/* binding */ Loading)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n\nfunction Loading() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-center items-center py-12\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"animate-spin rounded-full h-12 w-12 border-b-2 border-orange-500\"\n        }, void 0, false, {\n            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n            lineNumber: 4,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 3,\n        columnNumber: 5\n    }, this);\n}\n_c = Loading;\nfunction ProductCardSkeleton() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden animate-pulse\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"aspect-square bg-gray-300\"\n            }, void 0, false, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                lineNumber: 12,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4 space-y-3\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-4 bg-gray-300 rounded w-3/4\"\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 14,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-3 bg-gray-300 rounded w-1/2\"\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 15,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-5 bg-gray-300 rounded w-1/3\"\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 16,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-300 rounded w-1/4\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                                lineNumber: 18,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"h-3 bg-gray-300 rounded w-1/4\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                                lineNumber: 19,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 17,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"h-8 bg-gray-300 rounded\"\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                        lineNumber: 21,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                lineNumber: 13,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 11,\n        columnNumber: 5\n    }, this);\n}\n_c1 = ProductCardSkeleton;\nfunction ProductGridSkeleton(param) {\n    let { count = 8 } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6\",\n        children: Array.from({\n            length: count\n        }).map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(ProductCardSkeleton, {}, index, false, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n                lineNumber: 31,\n                columnNumber: 9\n            }, this))\n    }, void 0, false, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\Loading.tsx\",\n        lineNumber: 29,\n        columnNumber: 5\n    }, this);\n}\n_c2 = ProductGridSkeleton;\nvar _c, _c1, _c2;\n$RefreshReg$(_c, \"Loading\");\n$RefreshReg$(_c1, \"ProductCardSkeleton\");\n$RefreshReg$(_c2, \"ProductGridSkeleton\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/Loading.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ProductCard(param) {\n    let { product, onGetShortLink } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleGetShortLink = async ()=>{\n        if (!onGetShortLink) return;\n        setIsLoading(true);\n        try {\n            await onGetShortLink(product.productUrl);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Generate product image based on product name\n    const getProductImage = (productName)=>{\n        const keywords = productName.toLowerCase();\n        if (keywords.includes('iphone') || keywords.includes('apple watch')) {\n            return 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('samsung') || keywords.includes('galaxy')) {\n            return 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('macbook') || keywords.includes('laptop') || keywords.includes('dell') || keywords.includes('xps')) {\n            return 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('ipad') || keywords.includes('tablet')) {\n            return 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('airpods') || keywords.includes('earbuds') || keywords.includes('bose')) {\n            return 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('sony') || keywords.includes('wh-1000') || keywords.includes('headphone')) {\n            return 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('nintendo') || keywords.includes('switch')) {\n            return 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('playstation') || keywords.includes('ps5')) {\n            return 'https://images.unsplash.com/photo-1607853202273-797f1c22a38e?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('canon') || keywords.includes('camera')) {\n            return 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('gopro') || keywords.includes('hero')) {\n            return 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('dyson') || keywords.includes('vacuum')) {\n            return 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('kitchenaid') || keywords.includes('mixer')) {\n            return 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('instant pot') || keywords.includes('pressure cooker')) {\n            return 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('fitbit') || keywords.includes('fitness')) {\n            return 'https://images.unsplash.com/photo-1575311373937-040b8e1fd5b6?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('ring') || keywords.includes('doorbell')) {\n            return 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('echo') || keywords.includes('alexa')) {\n            return 'https://images.unsplash.com/photo-1543512214-318c7553f230?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('kindle') || keywords.includes('ebook')) {\n            return 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop&crop=center';\n        } else {\n            // Default tech product image\n            return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=center';\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('th-TH', {\n            style: 'currency',\n            currency: 'THB'\n        }).format(price);\n    };\n    const formatRating = (rating)=>{\n        return rating.toFixed(1);\n    };\n    const formatSold = (sold)=>{\n        if (sold >= 1000) {\n            return \"\".concat((sold / 1000).toFixed(1), \"k\");\n        }\n        return sold.toString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square bg-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: getProductImage(product.offerName),\n                        alt: product.offerName,\n                        className: \"w-full h-full object-cover rounded-t-lg\",\n                        onError: (e)=>{\n                            const target = e.target;\n                            target.src = '/placeholder-image.svg';\n                        }\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    product.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold\",\n                        children: [\n                            \"-\",\n                            product.discount,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-sm font-semibold\",\n                        children: [\n                            product.commissionRate.toFixed(1),\n                            \"% คอมมิชชั่น\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-800 mb-2 line-clamp-2 text-sm\",\n                        children: product.offerName\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-xs mb-2\",\n                        children: product.shopName\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600 font-bold text-lg\",\n                                children: formatPrice(product.currentPrice)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            product.originalPrice > product.currentPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 line-through text-sm\",\n                                children: formatPrice(product.originalPrice)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-sm text-gray-600 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-500\",\n                                        children: \"★\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: formatRating(product.rating)\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    formatSold(product.sold),\n                                    \" ขายแล้ว\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    product.categoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs\",\n                            children: product.categoryName\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: product.productUrl,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex-1 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded text-sm font-medium text-center transition-colors\",\n                                children: \"ดูสินค้า\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            onGetShortLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGetShortLink,\n                                disabled: isLoading,\n                                className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-2 px-4 rounded text-sm font-medium transition-colors\",\n                                children: isLoading ? '...' : 'ลิงก์สั้น'\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"EmvgwIb3cHpoFpeP+WmEDbjx4y4=\");\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL3NyYy9jb21wb25lbnRzL1Byb2R1Y3RDYXJkLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7QUFHaUM7QUFPbEIsU0FBU0MsWUFBWSxLQUE2QztRQUE3QyxFQUFFQyxPQUFPLEVBQUVDLGNBQWMsRUFBb0IsR0FBN0M7O0lBQ2xDLE1BQU0sQ0FBQ0MsV0FBV0MsYUFBYSxHQUFHTCwrQ0FBUUEsQ0FBQztJQUUzQyxNQUFNTSxxQkFBcUI7UUFDekIsSUFBSSxDQUFDSCxnQkFBZ0I7UUFFckJFLGFBQWE7UUFDYixJQUFJO1lBQ0YsTUFBTUYsZUFBZUQsUUFBUUssVUFBVTtRQUN6QyxTQUFVO1lBQ1JGLGFBQWE7UUFDZjtJQUNGO0lBRUEsK0NBQStDO0lBQy9DLE1BQU1HLGtCQUFrQixDQUFDQztRQUN2QixNQUFNQyxXQUFXRCxZQUFZRSxXQUFXO1FBRXhDLElBQUlELFNBQVNFLFFBQVEsQ0FBQyxhQUFhRixTQUFTRSxRQUFRLENBQUMsZ0JBQWdCO1lBQ25FLE9BQU87UUFDVCxPQUFPLElBQUlGLFNBQVNFLFFBQVEsQ0FBQyxjQUFjRixTQUFTRSxRQUFRLENBQUMsV0FBVztZQUN0RSxPQUFPO1FBQ1QsT0FBTyxJQUFJRixTQUFTRSxRQUFRLENBQUMsY0FBY0YsU0FBU0UsUUFBUSxDQUFDLGFBQWFGLFNBQVNFLFFBQVEsQ0FBQyxXQUFXRixTQUFTRSxRQUFRLENBQUMsUUFBUTtZQUMvSCxPQUFPO1FBQ1QsT0FBTyxJQUFJRixTQUFTRSxRQUFRLENBQUMsV0FBV0YsU0FBU0UsUUFBUSxDQUFDLFdBQVc7WUFDbkUsT0FBTztRQUNULE9BQU8sSUFBSUYsU0FBU0UsUUFBUSxDQUFDLGNBQWNGLFNBQVNFLFFBQVEsQ0FBQyxjQUFjRixTQUFTRSxRQUFRLENBQUMsU0FBUztZQUNwRyxPQUFPO1FBQ1QsT0FBTyxJQUFJRixTQUFTRSxRQUFRLENBQUMsV0FBV0YsU0FBU0UsUUFBUSxDQUFDLGNBQWNGLFNBQVNFLFFBQVEsQ0FBQyxjQUFjO1lBQ3RHLE9BQU87UUFDVCxPQUFPLElBQUlGLFNBQVNFLFFBQVEsQ0FBQyxlQUFlRixTQUFTRSxRQUFRLENBQUMsV0FBVztZQUN2RSxPQUFPO1FBQ1QsT0FBTyxJQUFJRixTQUFTRSxRQUFRLENBQUMsa0JBQWtCRixTQUFTRSxRQUFRLENBQUMsUUFBUTtZQUN2RSxPQUFPO1FBQ1QsT0FBTyxJQUFJRixTQUFTRSxRQUFRLENBQUMsWUFBWUYsU0FBU0UsUUFBUSxDQUFDLFdBQVc7WUFDcEUsT0FBTztRQUNULE9BQU8sSUFBSUYsU0FBU0UsUUFBUSxDQUFDLFlBQVlGLFNBQVNFLFFBQVEsQ0FBQyxTQUFTO1lBQ2xFLE9BQU87UUFDVCxPQUFPLElBQUlGLFNBQVNFLFFBQVEsQ0FBQyxZQUFZRixTQUFTRSxRQUFRLENBQUMsV0FBVztZQUNwRSxPQUFPO1FBQ1QsT0FBTyxJQUFJRixTQUFTRSxRQUFRLENBQUMsaUJBQWlCRixTQUFTRSxRQUFRLENBQUMsVUFBVTtZQUN4RSxPQUFPO1FBQ1QsT0FBTyxJQUFJRixTQUFTRSxRQUFRLENBQUMsa0JBQWtCRixTQUFTRSxRQUFRLENBQUMsb0JBQW9CO1lBQ25GLE9BQU87UUFDVCxPQUFPLElBQUlGLFNBQVNFLFFBQVEsQ0FBQyxhQUFhRixTQUFTRSxRQUFRLENBQUMsWUFBWTtZQUN0RSxPQUFPO1FBQ1QsT0FBTyxJQUFJRixTQUFTRSxRQUFRLENBQUMsV0FBV0YsU0FBU0UsUUFBUSxDQUFDLGFBQWE7WUFDckUsT0FBTztRQUNULE9BQU8sSUFBSUYsU0FBU0UsUUFBUSxDQUFDLFdBQVdGLFNBQVNFLFFBQVEsQ0FBQyxVQUFVO1lBQ2xFLE9BQU87UUFDVCxPQUFPLElBQUlGLFNBQVNFLFFBQVEsQ0FBQyxhQUFhRixTQUFTRSxRQUFRLENBQUMsVUFBVTtZQUNwRSxPQUFPO1FBQ1QsT0FBTztZQUNMLDZCQUE2QjtZQUM3QixPQUFPO1FBQ1Q7SUFDRjtJQUVBLE1BQU1DLGNBQWMsQ0FBQ0M7UUFDbkIsT0FBTyxJQUFJQyxLQUFLQyxZQUFZLENBQUMsU0FBUztZQUNwQ0MsT0FBTztZQUNQQyxVQUFVO1FBQ1osR0FBR0MsTUFBTSxDQUFDTDtJQUNaO0lBRUEsTUFBTU0sZUFBZSxDQUFDQztRQUNwQixPQUFPQSxPQUFPQyxPQUFPLENBQUM7SUFDeEI7SUFFQSxNQUFNQyxhQUFhLENBQUNDO1FBQ2xCLElBQUlBLFFBQVEsTUFBTTtZQUNoQixPQUFPLEdBQTRCLE9BQXpCLENBQUNBLE9BQU8sSUFBRyxFQUFHRixPQUFPLENBQUMsSUFBRztRQUNyQztRQUNBLE9BQU9FLEtBQUtDLFFBQVE7SUFDdEI7SUFFQSxxQkFDRSw4REFBQ0M7UUFBSUMsV0FBVTs7MEJBRWIsOERBQUNEO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ0M7d0JBQ0NDLEtBQUtyQixnQkFBZ0JOLFFBQVE0QixTQUFTO3dCQUN0Q0MsS0FBSzdCLFFBQVE0QixTQUFTO3dCQUN0QkgsV0FBVTt3QkFDVkssU0FBUyxDQUFDQzs0QkFDUixNQUFNQyxTQUFTRCxFQUFFQyxNQUFNOzRCQUN2QkEsT0FBT0wsR0FBRyxHQUFHO3dCQUNmOzs7Ozs7b0JBRUQzQixRQUFRaUMsUUFBUSxHQUFHLG1CQUNsQiw4REFBQ1Q7d0JBQUlDLFdBQVU7OzRCQUFzRjs0QkFDakd6QixRQUFRaUMsUUFBUTs0QkFBQzs7Ozs7OztrQ0FHdkIsOERBQUNUO3dCQUFJQyxXQUFVOzs0QkFDWnpCLFFBQVFrQyxjQUFjLENBQUNkLE9BQU8sQ0FBQzs0QkFBRzs7Ozs7Ozs7Ozs7OzswQkFLdkMsOERBQUNJO2dCQUFJQyxXQUFVOztrQ0FDYiw4REFBQ1U7d0JBQUdWLFdBQVU7a0NBQ1h6QixRQUFRNEIsU0FBUzs7Ozs7O2tDQUlwQiw4REFBQ1E7d0JBQUVYLFdBQVU7a0NBQThCekIsUUFBUXFDLFFBQVE7Ozs7OztrQ0FHM0QsOERBQUNiO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ2E7Z0NBQUtiLFdBQVU7MENBQ2JkLFlBQVlYLFFBQVF1QyxZQUFZOzs7Ozs7NEJBRWxDdkMsUUFBUXdDLGFBQWEsR0FBR3hDLFFBQVF1QyxZQUFZLGtCQUMzQyw4REFBQ0Q7Z0NBQUtiLFdBQVU7MENBQ2JkLFlBQVlYLFFBQVF3QyxhQUFhOzs7Ozs7Ozs7Ozs7a0NBTXhDLDhEQUFDaEI7d0JBQUlDLFdBQVU7OzBDQUNiLDhEQUFDRDtnQ0FBSUMsV0FBVTs7a0RBQ2IsOERBQUNhO3dDQUFLYixXQUFVO2tEQUFrQjs7Ozs7O2tEQUNsQyw4REFBQ2E7a0RBQU1wQixhQUFhbEIsUUFBUW1CLE1BQU07Ozs7Ozs7Ozs7OzswQ0FFcEMsOERBQUNtQjs7b0NBQU1qQixXQUFXckIsUUFBUXNCLElBQUk7b0NBQUU7Ozs7Ozs7Ozs7Ozs7b0JBSWpDdEIsUUFBUXlDLFlBQVksa0JBQ25CLDhEQUFDakI7d0JBQUlDLFdBQVU7a0NBQ2IsNEVBQUNhOzRCQUFLYixXQUFVO3NDQUNiekIsUUFBUXlDLFlBQVk7Ozs7Ozs7Ozs7O2tDQU0zQiw4REFBQ2pCO3dCQUFJQyxXQUFVOzswQ0FDYiw4REFBQ2lCO2dDQUNDQyxNQUFNM0MsUUFBUUssVUFBVTtnQ0FDeEIyQixRQUFPO2dDQUNQWSxLQUFJO2dDQUNKbkIsV0FBVTswQ0FDWDs7Ozs7OzRCQUdBeEIsZ0NBQ0MsOERBQUM0QztnQ0FDQ0MsU0FBUzFDO2dDQUNUMkMsVUFBVTdDO2dDQUNWdUIsV0FBVTswQ0FFVHZCLFlBQVksUUFBUTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBT25DO0dBakt3Qkg7S0FBQUEiLCJzb3VyY2VzIjpbIkg6XFxQUk9KRUNUXFxzaG9wZWVwcm9kdWN0XFxzcmNcXGNvbXBvbmVudHNcXFByb2R1Y3RDYXJkLnRzeCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IE9mZmVyTm9kZSB9IGZyb20gJ0AvbGliL3R5cGVzJztcbmltcG9ydCB7IHVzZVN0YXRlIH0gZnJvbSAncmVhY3QnO1xuXG5pbnRlcmZhY2UgUHJvZHVjdENhcmRQcm9wcyB7XG4gIHByb2R1Y3Q6IE9mZmVyTm9kZTtcbiAgb25HZXRTaG9ydExpbms/OiAocHJvZHVjdFVybDogc3RyaW5nKSA9PiB2b2lkO1xufVxuXG5leHBvcnQgZGVmYXVsdCBmdW5jdGlvbiBQcm9kdWN0Q2FyZCh7IHByb2R1Y3QsIG9uR2V0U2hvcnRMaW5rIH06IFByb2R1Y3RDYXJkUHJvcHMpIHtcbiAgY29uc3QgW2lzTG9hZGluZywgc2V0SXNMb2FkaW5nXSA9IHVzZVN0YXRlKGZhbHNlKTtcblxuICBjb25zdCBoYW5kbGVHZXRTaG9ydExpbmsgPSBhc3luYyAoKSA9PiB7XG4gICAgaWYgKCFvbkdldFNob3J0TGluaykgcmV0dXJuO1xuXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpO1xuICAgIHRyeSB7XG4gICAgICBhd2FpdCBvbkdldFNob3J0TGluayhwcm9kdWN0LnByb2R1Y3RVcmwpO1xuICAgIH0gZmluYWxseSB7XG4gICAgICBzZXRJc0xvYWRpbmcoZmFsc2UpO1xuICAgIH1cbiAgfTtcblxuICAvLyBHZW5lcmF0ZSBwcm9kdWN0IGltYWdlIGJhc2VkIG9uIHByb2R1Y3QgbmFtZVxuICBjb25zdCBnZXRQcm9kdWN0SW1hZ2UgPSAocHJvZHVjdE5hbWU6IHN0cmluZykgPT4ge1xuICAgIGNvbnN0IGtleXdvcmRzID0gcHJvZHVjdE5hbWUudG9Mb3dlckNhc2UoKTtcblxuICAgIGlmIChrZXl3b3Jkcy5pbmNsdWRlcygnaXBob25lJykgfHwga2V5d29yZHMuaW5jbHVkZXMoJ2FwcGxlIHdhdGNoJykpIHtcbiAgICAgIHJldHVybiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1OTI3NTA0NzUzMzgtNzRiN2IyMTA4NWFiP3c9NDAwJmg9NDAwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJztcbiAgICB9IGVsc2UgaWYgKGtleXdvcmRzLmluY2x1ZGVzKCdzYW1zdW5nJykgfHwga2V5d29yZHMuaW5jbHVkZXMoJ2dhbGF4eScpKSB7XG4gICAgICByZXR1cm4gJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNjEwOTQ1MjY1MDY0LTBlMzRlNTUxOWJiZj93PTQwMCZoPTQwMCZmaXQ9Y3JvcCZjcm9wPWNlbnRlcic7XG4gICAgfSBlbHNlIGlmIChrZXl3b3Jkcy5pbmNsdWRlcygnbWFjYm9vaycpIHx8IGtleXdvcmRzLmluY2x1ZGVzKCdsYXB0b3AnKSB8fCBrZXl3b3Jkcy5pbmNsdWRlcygnZGVsbCcpIHx8IGtleXdvcmRzLmluY2x1ZGVzKCd4cHMnKSkge1xuICAgICAgcmV0dXJuICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU0MTgwNzA4NC01YzUyYjZiM2FkZWY/dz00MDAmaD00MDAmZml0PWNyb3AmY3JvcD1jZW50ZXInO1xuICAgIH0gZWxzZSBpZiAoa2V5d29yZHMuaW5jbHVkZXMoJ2lwYWQnKSB8fCBrZXl3b3Jkcy5pbmNsdWRlcygndGFibGV0JykpIHtcbiAgICAgIHJldHVybiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NDQyNDQwMTUtMGRmNGIzZmZjNmIwP3c9NDAwJmg9NDAwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJztcbiAgICB9IGVsc2UgaWYgKGtleXdvcmRzLmluY2x1ZGVzKCdhaXJwb2RzJykgfHwga2V5d29yZHMuaW5jbHVkZXMoJ2VhcmJ1ZHMnKSB8fCBrZXl3b3Jkcy5pbmNsdWRlcygnYm9zZScpKSB7XG4gICAgICByZXR1cm4gJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTcyNTY5NTExMjU0LWQ4ZjkyNWZlMmNiYj93PTQwMCZoPTQwMCZmaXQ9Y3JvcCZjcm9wPWNlbnRlcic7XG4gICAgfSBlbHNlIGlmIChrZXl3b3Jkcy5pbmNsdWRlcygnc29ueScpIHx8IGtleXdvcmRzLmluY2x1ZGVzKCd3aC0xMDAwJykgfHwga2V5d29yZHMuaW5jbHVkZXMoJ2hlYWRwaG9uZScpKSB7XG4gICAgICByZXR1cm4gJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTgzMzk0ODM4MzM2LWFjZDk3NzczNmY5MD93PTQwMCZoPTQwMCZmaXQ9Y3JvcCZjcm9wPWNlbnRlcic7XG4gICAgfSBlbHNlIGlmIChrZXl3b3Jkcy5pbmNsdWRlcygnbmludGVuZG8nKSB8fCBrZXl3b3Jkcy5pbmNsdWRlcygnc3dpdGNoJykpIHtcbiAgICAgIHJldHVybiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE2MDYxNDQwNDI2MTQtYjI0MTdlOTljNGUzP3c9NDAwJmg9NDAwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJztcbiAgICB9IGVsc2UgaWYgKGtleXdvcmRzLmluY2x1ZGVzKCdwbGF5c3RhdGlvbicpIHx8IGtleXdvcmRzLmluY2x1ZGVzKCdwczUnKSkge1xuICAgICAgcmV0dXJuICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTYwNzg1MzIwMjI3My03OTdmMWMyMmEzOGU/dz00MDAmaD00MDAmZml0PWNyb3AmY3JvcD1jZW50ZXInO1xuICAgIH0gZWxzZSBpZiAoa2V5d29yZHMuaW5jbHVkZXMoJ2Nhbm9uJykgfHwga2V5d29yZHMuaW5jbHVkZXMoJ2NhbWVyYScpKSB7XG4gICAgICByZXR1cm4gJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTAyOTIwOTE3MTI4LTFhYTUwMDc2NGNiZD93PTQwMCZoPTQwMCZmaXQ9Y3JvcCZjcm9wPWNlbnRlcic7XG4gICAgfSBlbHNlIGlmIChrZXl3b3Jkcy5pbmNsdWRlcygnZ29wcm8nKSB8fCBrZXl3b3Jkcy5pbmNsdWRlcygnaGVybycpKSB7XG4gICAgICByZXR1cm4gJ2h0dHBzOi8vaW1hZ2VzLnVuc3BsYXNoLmNvbS9waG90by0xNTUxNjk4NjE4LTFkZmU1ZDk3ZDI1Nj93PTQwMCZoPTQwMCZmaXQ9Y3JvcCZjcm9wPWNlbnRlcic7XG4gICAgfSBlbHNlIGlmIChrZXl3b3Jkcy5pbmNsdWRlcygnZHlzb24nKSB8fCBrZXl3b3Jkcy5pbmNsdWRlcygndmFjdXVtJykpIHtcbiAgICAgIHJldHVybiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NTg2MTg2NjYtZmNkMjVjODVjZDY0P3c9NDAwJmg9NDAwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJztcbiAgICB9IGVsc2UgaWYgKGtleXdvcmRzLmluY2x1ZGVzKCdraXRjaGVuYWlkJykgfHwga2V5d29yZHMuaW5jbHVkZXMoJ21peGVyJykpIHtcbiAgICAgIHJldHVybiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NTY5MDkxMTQtZjZlN2FkN2QzMTM2P3c9NDAwJmg9NDAwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJztcbiAgICB9IGVsc2UgaWYgKGtleXdvcmRzLmluY2x1ZGVzKCdpbnN0YW50IHBvdCcpIHx8IGtleXdvcmRzLmluY2x1ZGVzKCdwcmVzc3VyZSBjb29rZXInKSkge1xuICAgICAgcmV0dXJuICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU1NjkwOTExNC1mNmU3YWQ3ZDMxMzY/dz00MDAmaD00MDAmZml0PWNyb3AmY3JvcD1jZW50ZXInO1xuICAgIH0gZWxzZSBpZiAoa2V5d29yZHMuaW5jbHVkZXMoJ2ZpdGJpdCcpIHx8IGtleXdvcmRzLmluY2x1ZGVzKCdmaXRuZXNzJykpIHtcbiAgICAgIHJldHVybiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NzUzMTEzNzM5MzctMDQwYjhlMWZkNWI2P3c9NDAwJmg9NDAwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJztcbiAgICB9IGVsc2UgaWYgKGtleXdvcmRzLmluY2x1ZGVzKCdyaW5nJykgfHwga2V5d29yZHMuaW5jbHVkZXMoJ2Rvb3JiZWxsJykpIHtcbiAgICAgIHJldHVybiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NTg2MTg2NjYtZmNkMjVjODVjZDY0P3c9NDAwJmg9NDAwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJztcbiAgICB9IGVsc2UgaWYgKGtleXdvcmRzLmluY2x1ZGVzKCdlY2hvJykgfHwga2V5d29yZHMuaW5jbHVkZXMoJ2FsZXhhJykpIHtcbiAgICAgIHJldHVybiAnaHR0cHM6Ly9pbWFnZXMudW5zcGxhc2guY29tL3Bob3RvLTE1NDM1MTIyMTQtMzE4Yzc1NTNmMjMwP3c9NDAwJmg9NDAwJmZpdD1jcm9wJmNyb3A9Y2VudGVyJztcbiAgICB9IGVsc2UgaWYgKGtleXdvcmRzLmluY2x1ZGVzKCdraW5kbGUnKSB8fCBrZXl3b3Jkcy5pbmNsdWRlcygnZWJvb2snKSkge1xuICAgICAgcmV0dXJuICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTQ4MTYyNzgzNDg3Ni1iNzgzM2U4ZjU1NzA/dz00MDAmaD00MDAmZml0PWNyb3AmY3JvcD1jZW50ZXInO1xuICAgIH0gZWxzZSB7XG4gICAgICAvLyBEZWZhdWx0IHRlY2ggcHJvZHVjdCBpbWFnZVxuICAgICAgcmV0dXJuICdodHRwczovL2ltYWdlcy51bnNwbGFzaC5jb20vcGhvdG8tMTU2MDQ3MjM1NC1iMzNmZjBjNDRhNDM/dz00MDAmaD00MDAmZml0PWNyb3AmY3JvcD1jZW50ZXInO1xuICAgIH1cbiAgfTtcblxuICBjb25zdCBmb3JtYXRQcmljZSA9IChwcmljZTogbnVtYmVyKSA9PiB7XG4gICAgcmV0dXJuIG5ldyBJbnRsLk51bWJlckZvcm1hdCgndGgtVEgnLCB7XG4gICAgICBzdHlsZTogJ2N1cnJlbmN5JyxcbiAgICAgIGN1cnJlbmN5OiAnVEhCJyxcbiAgICB9KS5mb3JtYXQocHJpY2UpO1xuICB9O1xuXG4gIGNvbnN0IGZvcm1hdFJhdGluZyA9IChyYXRpbmc6IG51bWJlcikgPT4ge1xuICAgIHJldHVybiByYXRpbmcudG9GaXhlZCgxKTtcbiAgfTtcblxuICBjb25zdCBmb3JtYXRTb2xkID0gKHNvbGQ6IG51bWJlcikgPT4ge1xuICAgIGlmIChzb2xkID49IDEwMDApIHtcbiAgICAgIHJldHVybiBgJHsoc29sZCAvIDEwMDApLnRvRml4ZWQoMSl9a2A7XG4gICAgfVxuICAgIHJldHVybiBzb2xkLnRvU3RyaW5nKCk7XG4gIH07XG5cbiAgcmV0dXJuIChcbiAgICA8ZGl2IGNsYXNzTmFtZT1cImJnLXdoaXRlIHJvdW5kZWQtbGcgc2hhZG93LW1kIG92ZXJmbG93LWhpZGRlbiBob3ZlcjpzaGFkb3ctbGcgdHJhbnNpdGlvbi1zaGFkb3cgZHVyYXRpb24tMzAwXCI+XG4gICAgICB7LyogUHJvZHVjdCBJbWFnZSAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwicmVsYXRpdmUgYXNwZWN0LXNxdWFyZSBiZy1ncmF5LTEwMFwiPlxuICAgICAgICA8aW1nXG4gICAgICAgICAgc3JjPXtnZXRQcm9kdWN0SW1hZ2UocHJvZHVjdC5vZmZlck5hbWUpfVxuICAgICAgICAgIGFsdD17cHJvZHVjdC5vZmZlck5hbWV9XG4gICAgICAgICAgY2xhc3NOYW1lPVwidy1mdWxsIGgtZnVsbCBvYmplY3QtY292ZXIgcm91bmRlZC10LWxnXCJcbiAgICAgICAgICBvbkVycm9yPXsoZTogUmVhY3QuU3ludGhldGljRXZlbnQ8SFRNTEltYWdlRWxlbWVudCwgRXZlbnQ+KSA9PiB7XG4gICAgICAgICAgICBjb25zdCB0YXJnZXQgPSBlLnRhcmdldCBhcyBIVE1MSW1hZ2VFbGVtZW50O1xuICAgICAgICAgICAgdGFyZ2V0LnNyYyA9ICcvcGxhY2Vob2xkZXItaW1hZ2Uuc3ZnJztcbiAgICAgICAgICB9fVxuICAgICAgICAvPlxuICAgICAgICB7cHJvZHVjdC5kaXNjb3VudCA+IDAgJiYgKFxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgbGVmdC0yIGJnLXJlZC01MDAgdGV4dC13aGl0ZSBweC0yIHB5LTEgcm91bmRlZCB0ZXh0LXNtIGZvbnQtc2VtaWJvbGRcIj5cbiAgICAgICAgICAgIC17cHJvZHVjdC5kaXNjb3VudH0lXG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgICl9XG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiYWJzb2x1dGUgdG9wLTIgcmlnaHQtMiBiZy1vcmFuZ2UtNTAwIHRleHQtd2hpdGUgcHgtMiBweS0xIHJvdW5kZWQgdGV4dC1zbSBmb250LXNlbWlib2xkXCI+XG4gICAgICAgICAge3Byb2R1Y3QuY29tbWlzc2lvblJhdGUudG9GaXhlZCgxKX0lIOC4hOC4reC4oeC4oeC4tOC4iuC4iuC4seC5iOC4mVxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUHJvZHVjdCBJbmZvICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJwLTRcIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT1cImZvbnQtc2VtaWJvbGQgdGV4dC1ncmF5LTgwMCBtYi0yIGxpbmUtY2xhbXAtMiB0ZXh0LXNtXCI+XG4gICAgICAgICAge3Byb2R1Y3Qub2ZmZXJOYW1lfVxuICAgICAgICA8L2gzPlxuXG4gICAgICAgIHsvKiBTaG9wIE5hbWUgKi99XG4gICAgICAgIDxwIGNsYXNzTmFtZT1cInRleHQtZ3JheS02MDAgdGV4dC14cyBtYi0yXCI+e3Byb2R1Y3Quc2hvcE5hbWV9PC9wPlxuXG4gICAgICAgIHsvKiBQcmljZSAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBnYXAtMiBtYi0yXCI+XG4gICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1vcmFuZ2UtNjAwIGZvbnQtYm9sZCB0ZXh0LWxnXCI+XG4gICAgICAgICAgICB7Zm9ybWF0UHJpY2UocHJvZHVjdC5jdXJyZW50UHJpY2UpfVxuICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICB7cHJvZHVjdC5vcmlnaW5hbFByaWNlID4gcHJvZHVjdC5jdXJyZW50UHJpY2UgJiYgKFxuICAgICAgICAgICAgPHNwYW4gY2xhc3NOYW1lPVwidGV4dC1ncmF5LTQwMCBsaW5lLXRocm91Z2ggdGV4dC1zbVwiPlxuICAgICAgICAgICAgICB7Zm9ybWF0UHJpY2UocHJvZHVjdC5vcmlnaW5hbFByaWNlKX1cbiAgICAgICAgICAgIDwvc3Bhbj5cbiAgICAgICAgICApfVxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICB7LyogUmF0aW5nIGFuZCBTb2xkICovfVxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIGp1c3RpZnktYmV0d2VlbiB0ZXh0LXNtIHRleHQtZ3JheS02MDAgbWItM1wiPlxuICAgICAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZmxleCBpdGVtcy1jZW50ZXIgZ2FwLTFcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cInRleHQteWVsbG93LTUwMFwiPuKYhTwvc3Bhbj5cbiAgICAgICAgICAgIDxzcGFuPntmb3JtYXRSYXRpbmcocHJvZHVjdC5yYXRpbmcpfTwvc3Bhbj5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgICA8c3Bhbj57Zm9ybWF0U29sZChwcm9kdWN0LnNvbGQpfSDguILguLLguKLguYHguKXguYnguKc8L3NwYW4+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIHsvKiBDYXRlZ29yeSAqL31cbiAgICAgICAge3Byb2R1Y3QuY2F0ZWdvcnlOYW1lICYmIChcbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cIm1iLTNcIj5cbiAgICAgICAgICAgIDxzcGFuIGNsYXNzTmFtZT1cImlubGluZS1ibG9jayBiZy1ncmF5LTEwMCB0ZXh0LWdyYXktNzAwIHB4LTIgcHktMSByb3VuZGVkIHRleHQteHNcIj5cbiAgICAgICAgICAgICAge3Byb2R1Y3QuY2F0ZWdvcnlOYW1lfVxuICAgICAgICAgICAgPC9zcGFuPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICApfVxuXG4gICAgICAgIHsvKiBBY3Rpb24gQnV0dG9ucyAqL31cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGdhcC0yXCI+XG4gICAgICAgICAgPGFcbiAgICAgICAgICAgIGhyZWY9e3Byb2R1Y3QucHJvZHVjdFVybH1cbiAgICAgICAgICAgIHRhcmdldD1cIl9ibGFua1wiXG4gICAgICAgICAgICByZWw9XCJub29wZW5lciBub3JlZmVycmVyXCJcbiAgICAgICAgICAgIGNsYXNzTmFtZT1cImZsZXgtMSBiZy1vcmFuZ2UtNTAwIGhvdmVyOmJnLW9yYW5nZS02MDAgdGV4dC13aGl0ZSBweS0yIHB4LTQgcm91bmRlZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRleHQtY2VudGVyIHRyYW5zaXRpb24tY29sb3JzXCJcbiAgICAgICAgICA+XG4gICAgICAgICAgICDguJTguLnguKrguLTguJnguITguYnguLJcbiAgICAgICAgICA8L2E+XG4gICAgICAgICAge29uR2V0U2hvcnRMaW5rICYmIChcbiAgICAgICAgICAgIDxidXR0b25cbiAgICAgICAgICAgICAgb25DbGljaz17aGFuZGxlR2V0U2hvcnRMaW5rfVxuICAgICAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJiZy1ibHVlLTUwMCBob3ZlcjpiZy1ibHVlLTYwMCBkaXNhYmxlZDpiZy1ncmF5LTQwMCB0ZXh0LXdoaXRlIHB5LTIgcHgtNCByb3VuZGVkIHRleHQtc20gZm9udC1tZWRpdW0gdHJhbnNpdGlvbi1jb2xvcnNcIlxuICAgICAgICAgICAgPlxuICAgICAgICAgICAgICB7aXNMb2FkaW5nID8gJy4uLicgOiAn4Lil4Li04LiH4LiB4LmM4Liq4Lix4LmJ4LiZJ31cbiAgICAgICAgICAgIDwvYnV0dG9uPlxuICAgICAgICAgICl9XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG4gICAgPC9kaXY+XG4gICk7XG59XG4iXSwibmFtZXMiOlsidXNlU3RhdGUiLCJQcm9kdWN0Q2FyZCIsInByb2R1Y3QiLCJvbkdldFNob3J0TGluayIsImlzTG9hZGluZyIsInNldElzTG9hZGluZyIsImhhbmRsZUdldFNob3J0TGluayIsInByb2R1Y3RVcmwiLCJnZXRQcm9kdWN0SW1hZ2UiLCJwcm9kdWN0TmFtZSIsImtleXdvcmRzIiwidG9Mb3dlckNhc2UiLCJpbmNsdWRlcyIsImZvcm1hdFByaWNlIiwicHJpY2UiLCJJbnRsIiwiTnVtYmVyRm9ybWF0Iiwic3R5bGUiLCJjdXJyZW5jeSIsImZvcm1hdCIsImZvcm1hdFJhdGluZyIsInJhdGluZyIsInRvRml4ZWQiLCJmb3JtYXRTb2xkIiwic29sZCIsInRvU3RyaW5nIiwiZGl2IiwiY2xhc3NOYW1lIiwiaW1nIiwic3JjIiwib2ZmZXJOYW1lIiwiYWx0Iiwib25FcnJvciIsImUiLCJ0YXJnZXQiLCJkaXNjb3VudCIsImNvbW1pc3Npb25SYXRlIiwiaDMiLCJwIiwic2hvcE5hbWUiLCJzcGFuIiwiY3VycmVudFByaWNlIiwib3JpZ2luYWxQcmljZSIsImNhdGVnb3J5TmFtZSIsImEiLCJocmVmIiwicmVsIiwiYnV0dG9uIiwib25DbGljayIsImRpc2FibGVkIl0sImlnbm9yZUxpc3QiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCard.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./src/components/SearchBar.tsx":
/*!**************************************!*\
  !*** ./src/components/SearchBar.tsx ***!
  \**************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ SearchBar)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction SearchBar(param) {\n    let { onSearch, isLoading = false } = param;\n    _s();\n    const [keyword, setKeyword] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [minPrice, setMinPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [maxPrice, setMaxPrice] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [sortBy, setSortBy] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('sales');\n    const [showFilters, setShowFilters] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSubmit = (e)=>{\n        e.preventDefault();\n        const searchParams = {\n            keyword: keyword.trim() || undefined,\n            minPrice: minPrice ? parseFloat(minPrice) : undefined,\n            maxPrice: maxPrice ? parseFloat(maxPrice) : undefined,\n            sortBy,\n            limit: 20\n        };\n        onSearch(searchParams);\n    };\n    const handleReset = ()=>{\n        setKeyword('');\n        setMinPrice('');\n        setMaxPrice('');\n        setSortBy('sales');\n        onSearch({\n            limit: 20\n        });\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md p-6 mb-6\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n            onSubmit: handleSubmit,\n            className: \"space-y-4\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex gap-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex-1\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                type: \"text\",\n                                value: keyword,\n                                onChange: (e)=>setKeyword(e.target.value),\n                                placeholder: \"ค้นหาสินค้า...\",\n                                className: \"w-full px-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                lineNumber: 46,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                            lineNumber: 45,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"submit\",\n                            disabled: isLoading,\n                            className: \"bg-orange-500 hover:bg-orange-600 disabled:bg-gray-400 text-white px-6 py-2 rounded-lg font-medium transition-colors\",\n                            children: isLoading ? 'กำลังค้นหา...' : 'ค้นหา'\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                            lineNumber: 54,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            type: \"button\",\n                            onClick: ()=>setShowFilters(!showFilters),\n                            className: \"bg-gray-500 hover:bg-gray-600 text-white px-4 py-2 rounded-lg font-medium transition-colors\",\n                            children: \"ตัวกรอง\"\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                            lineNumber: 61,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                    lineNumber: 44,\n                    columnNumber: 9\n                }, this),\n                showFilters && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"border-t pt-4 space-y-4\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-3 gap-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"ราคาต่ำสุด (บาท)\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                            lineNumber: 76,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: minPrice,\n                                            onChange: (e)=>setMinPrice(e.target.value),\n                                            placeholder: \"0\",\n                                            min: \"0\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                            lineNumber: 79,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"ราคาสูงสุด (บาท)\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                            type: \"number\",\n                                            value: maxPrice,\n                                            onChange: (e)=>setMaxPrice(e.target.value),\n                                            placeholder: \"ไม่จำกัด\",\n                                            min: \"0\",\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-transparent\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                            lineNumber: 93,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                    lineNumber: 89,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"label\", {\n                                            className: \"block text-sm font-medium text-gray-700 mb-1\",\n                                            children: \"เรียงตาม\"\n                                        }, void 0, false, {\n                                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                            lineNumber: 105,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"select\", {\n                                            value: sortBy,\n                                            onChange: (e)=>setSortBy(e.target.value),\n                                            className: \"w-full px-3 py-2 border border-gray-300 rounded focus:ring-2 focus:ring-orange-500 focus:border-transparent\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"sales\",\n                                                    children: \"ยอดขายสูงสุด\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"rating\",\n                                                    children: \"คะแนนสูงสุด\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                                    lineNumber: 114,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"price_asc\",\n                                                    children: \"ราคาต่ำสุด\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                                    lineNumber: 115,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"option\", {\n                                                    value: \"price_desc\",\n                                                    children: \"ราคาสูงสุด\"\n                                                }, void 0, false, {\n                                                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                            lineNumber: 108,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex gap-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                type: \"button\",\n                                onClick: handleReset,\n                                className: \"bg-gray-300 hover:bg-gray-400 text-gray-700 px-4 py-2 rounded font-medium transition-colors\",\n                                children: \"รีเซ็ต\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                            lineNumber: 122,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 11\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n            lineNumber: 42,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\SearchBar.tsx\",\n        lineNumber: 41,\n        columnNumber: 5\n    }, this);\n}\n_s(SearchBar, \"NK+FcHj6jgFJZUlrHb2r8xpeyrw=\");\n_c = SearchBar;\nvar _c;\n$RefreshReg$(_c, \"SearchBar\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/SearchBar.tsx\n"));

/***/ })

},
/******/ __webpack_require__ => { // webpackRuntimeModules
/******/ var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
/******/ __webpack_require__.O(0, ["main-app"], () => (__webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22H%3A%5C%5CPROJECT%5C%5Cshopeeproduct%5C%5Csrc%5C%5Capp%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=false!")));
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);