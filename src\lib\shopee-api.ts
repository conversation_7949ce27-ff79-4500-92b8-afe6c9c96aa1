import crypto from 'crypto';
import {
  ShopeeApiResponse,
  OfferList,
  BrandOffer,
  ShortLinkResponse,
  ConversionReport,
  SearchParams,
  GraphQLRequest,
  AuthHeader
} from './types';

class ShopeeApiClient {
  private appId: string;
  private secret: string;
  private endpoint: string;

  constructor() {
    this.appId = process.env.SHOPEE_APP_ID || '';
    this.secret = process.env.SHOPEE_SECRET || '';
    this.endpoint = process.env.SHOPEE_API_ENDPOINT || 'https://open-api.affiliate.shopee.co.th/graphql';
  }

  /**
   * Generate SHA256 signature for Shopee API authentication
   */
  private generateSignature(payload: string, timestamp: number): string {
    const signatureFactor = `${this.appId}${timestamp}${payload}${this.secret}`;
    return crypto.createHash('sha256').update(signatureFactor).digest('hex');
  }

  /**
   * Generate authorization header
   */
  private generateAuthHeader(payload: string): string {
    const timestamp = Math.floor(Date.now() / 1000);
    const signature = this.generateSignature(payload, timestamp);
    
    return `SHA256 Credential=${this.appId}, Timestamp=${timestamp}, Signature=${signature}`;
  }

  /**
   * Make GraphQL request to Shopee API
   */
  private async makeRequest<T>(request: GraphQLRequest): Promise<ShopeeApiResponse<T>> {
    const payload = JSON.stringify(request);
    const authHeader = this.generateAuthHeader(payload);

    try {
      const response = await fetch(this.endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': authHeader,
        },
        body: payload,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const result: ShopeeApiResponse<T> = await response.json();
      return result;
    } catch (error) {
      console.error('Shopee API request failed:', error);
      throw error;
    }
  }

  /**
   * Get product offers using v2 API
   */
  async getBrandOffers(): Promise<ShopeeApiResponse<{ productOfferV2: BrandOffer }>> {
    const query = `
      {
        productOfferV2 {
          nodes {
            commissionRate
          }
        }
      }
    `;

    return this.makeRequest<{ productOfferV2: BrandOffer }>({
      query,
    });
  }

  /**
   * Search offers with parameters using v2 API
   */
  async searchOffers(params: SearchParams = {}): Promise<ShopeeApiResponse<{ productOfferV2: BrandOffer }>> {
    const {
      keyword = '',
      categoryId,
      minPrice,
      maxPrice,
      sortBy = 'sales',
      limit = 20,
      scrollId
    } = params;

    // Use productOfferV2 for now as a basic implementation
    let query = `
      {
        productOfferV2 {
          nodes {
            commissionRate
          }
        }
      }
    `;

    return this.makeRequest<{ productOfferV2: BrandOffer }>({
      query,
    });
  }

  /**
   * Get short link for a product
   */
  async getShortLink(originalLink: string): Promise<ShopeeApiResponse<{ shortLink: ShortLinkResponse }>> {
    const query = `
      mutation {
        shortLink(originalLink: "${originalLink}") {
          shortLink
          originalLink
        }
      }
    `;

    return this.makeRequest<{ shortLink: ShortLinkResponse }>({
      query,
    });
  }

  /**
   * Get conversion report
   */
  async getConversionReport(
    purchaseTimeStart: number,
    purchaseTimeEnd: number,
    scrollId?: string
  ): Promise<ShopeeApiResponse<{ conversionReport: ConversionReport }>> {
    const query = `
      {
        conversionReport(
          purchaseTimeStart: ${purchaseTimeStart}
          purchaseTimeEnd: ${purchaseTimeEnd}
          ${scrollId ? `scrollId: "${scrollId}"` : ''}
        ) {
          nodes {
            orderId
            orderStatus
            commissionAmount
            commissionRate
            purchaseTime
            clickTime
            offerName
            shopName
          }
          totalCount
          scrollId
        }
      }
    `;

    return this.makeRequest<{ conversionReport: ConversionReport }>({
      query,
    });
  }
}

export const shopeeApi = new ShopeeApiClient();
