"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ProductCard(param) {\n    let { product, onGetShortLink } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleGetShortLink = async ()=>{\n        if (!onGetShortLink) return;\n        setIsLoading(true);\n        try {\n            await onGetShortLink(product.productUrl);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Generate product image based on product name\n    const getProductImage = (productName)=>{\n        const keywords = productName.toLowerCase();\n        if (keywords.includes('iphone') || keywords.includes('apple watch')) {\n            return 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('samsung') || keywords.includes('galaxy')) {\n            return 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('macbook') || keywords.includes('laptop') || keywords.includes('dell') || keywords.includes('xps')) {\n            return 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('ipad') || keywords.includes('tablet')) {\n            return 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('airpods') || keywords.includes('earbuds') || keywords.includes('bose')) {\n            return 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('sony') || keywords.includes('wh-1000') || keywords.includes('headphone')) {\n            return 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('nintendo') || keywords.includes('switch')) {\n            return 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('playstation') || keywords.includes('ps5')) {\n            return 'https://images.unsplash.com/photo-1607853202273-797f1c22a38e?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('canon') || keywords.includes('camera')) {\n            return 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('gopro') || keywords.includes('hero')) {\n            return 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('dyson') || keywords.includes('vacuum')) {\n            return 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('kitchenaid') || keywords.includes('mixer')) {\n            return 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('instant pot') || keywords.includes('pressure cooker')) {\n            return 'https://images.unsplash.com/photo-1556909114-f6e7ad7d3136?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('fitbit') || keywords.includes('fitness')) {\n            return 'https://images.unsplash.com/photo-1575311373937-040b8e1fd5b6?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('ring') || keywords.includes('doorbell')) {\n            return 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('echo') || keywords.includes('alexa')) {\n            return 'https://images.unsplash.com/photo-1543512214-318c7553f230?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('kindle') || keywords.includes('ebook')) {\n            return 'https://images.unsplash.com/photo-1481627834876-b7833e8f5570?w=400&h=400&fit=crop&crop=center';\n        } else {\n            // Default tech product image\n            return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=center';\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('th-TH', {\n            style: 'currency',\n            currency: 'THB'\n        }).format(price);\n    };\n    const formatRating = (rating)=>{\n        return rating.toFixed(1);\n    };\n    const formatSold = (sold)=>{\n        if (sold >= 1000) {\n            return \"\".concat((sold / 1000).toFixed(1), \"k\");\n        }\n        return sold.toString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square bg-gray-100\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                        src: getProductImage(product.offerName),\n                        alt: product.offerName,\n                        className: \"w-full h-full object-cover rounded-t-lg\",\n                        onError: (e)=>{\n                            const target = e.target;\n                            target.src = '/placeholder-image.svg';\n                        }\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this),\n                    product.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold\",\n                        children: [\n                            \"-\",\n                            product.discount,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 101,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-sm font-semibold\",\n                        children: [\n                            product.commissionRate.toFixed(1),\n                            \"% คอมมิชชั่น\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 105,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                lineNumber: 90,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-800 mb-2 line-clamp-2 text-sm\",\n                        children: product.offerName\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 112,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-xs mb-2\",\n                        children: product.shopName\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 117,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600 font-bold text-lg\",\n                                children: formatPrice(product.currentPrice)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 121,\n                                columnNumber: 11\n                            }, this),\n                            product.originalPrice > product.currentPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 line-through text-sm\",\n                                children: formatPrice(product.originalPrice)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 125,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 120,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-sm text-gray-600 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-500\",\n                                        children: \"★\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: formatRating(product.rating)\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                        lineNumber: 135,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    formatSold(product.sold),\n                                    \" ขายแล้ว\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 9\n                    }, this),\n                    product.categoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs\",\n                            children: product.categoryName\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 142,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: product.productUrl,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex-1 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded text-sm font-medium text-center transition-colors\",\n                                children: \"ดูสินค้า\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 151,\n                                columnNumber: 11\n                            }, this),\n                            onGetShortLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGetShortLink,\n                                disabled: isLoading,\n                                className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-2 px-4 rounded text-sm font-medium transition-colors\",\n                                children: isLoading ? '...' : 'ลิงก์สั้น'\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 160,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 150,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                lineNumber: 111,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n        lineNumber: 88,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"EmvgwIb3cHpoFpeP+WmEDbjx4y4=\");\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCard.tsx\n"));

/***/ })

});