import { NextResponse } from 'next/server';
import { shopeeApi } from '@/lib/shopee-api';

export async function GET() {
  try {
    const result = await shopeeApi.getBrandOffers();

    if (result.errors) {
      return NextResponse.json(
        { error: 'API Error', details: result.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(result.data);
  } catch (error) {
    console.error('Brand Offers API Error:', error);
    return NextResponse.json(
      { error: 'Internal Server Error', message: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
