import { NextRequest, NextResponse } from 'next/server';
import { shopeeApi } from '@/lib/shopee-api';

export async function POST(request: NextRequest) {
  try {
    const { originalLink } = await request.json();

    if (!originalLink) {
      return NextResponse.json(
        { error: 'Original link is required' },
        { status: 400 }
      );
    }

    const result = await shopeeApi.getShortLink(originalLink);
    
    if (result.errors) {
      return NextResponse.json(
        { error: 'API Error', details: result.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(result.data);
  } catch (error) {
    console.error('Short Link API Error:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
