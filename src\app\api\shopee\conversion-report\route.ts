import { NextRequest, NextResponse } from 'next/server';
import { shopeeApi } from '@/lib/shopee-api';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    const purchaseTimeStart = searchParams.get('purchaseTimeStart');
    const purchaseTimeEnd = searchParams.get('purchaseTimeEnd');
    const scrollId = searchParams.get('scrollId') || undefined;

    if (!purchaseTimeStart || !purchaseTimeEnd) {
      return NextResponse.json(
        { error: 'purchaseTimeStart and purchaseTimeEnd are required' },
        { status: 400 }
      );
    }

    const result = await shopeeApi.getConversionReport(
      parseInt(purchaseTimeStart),
      parseInt(purchaseTimeEnd),
      scrollId
    );
    
    if (result.errors) {
      return NextResponse.json(
        { error: 'API Error', details: result.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(result.data);
  } catch (error) {
    console.error('Conversion Report API Error:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { purchaseTimeStart, purchaseTimeEnd, scrollId } = await request.json();

    if (!purchaseTimeStart || !purchaseTimeEnd) {
      return NextResponse.json(
        { error: 'purchaseTimeStart and purchaseTimeEnd are required' },
        { status: 400 }
      );
    }

    const result = await shopeeApi.getConversionReport(
      purchaseTimeStart,
      purchaseTimeEnd,
      scrollId
    );
    
    if (result.errors) {
      return NextResponse.json(
        { error: 'API Error', details: result.errors },
        { status: 400 }
      );
    }

    return NextResponse.json(result.data);
  } catch (error) {
    console.error('Conversion Report API Error:', error);
    return NextResponse.json(
      { error: 'Internal Server Error' },
      { status: 500 }
    );
  }
}
