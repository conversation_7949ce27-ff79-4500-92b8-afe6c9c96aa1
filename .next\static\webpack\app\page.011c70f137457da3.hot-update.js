"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/page",{

/***/ "(app-pages-browser)/./src/components/ProductCard.tsx":
/*!****************************************!*\
  !*** ./src/components/ProductCard.tsx ***!
  \****************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ ProductCard)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\nfunction ProductCard(param) {\n    let { product, onGetShortLink } = param;\n    _s();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleGetShortLink = async ()=>{\n        if (!onGetShortLink) return;\n        setIsLoading(true);\n        try {\n            await onGetShortLink(product.productUrl);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    // Generate product image based on product name\n    const getProductImage = (productName)=>{\n        const keywords = productName.toLowerCase();\n        if (keywords.includes('iphone') || keywords.includes('apple')) {\n            return 'https://images.unsplash.com/photo-1592750475338-74b7b21085ab?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('samsung') || keywords.includes('galaxy')) {\n            return 'https://images.unsplash.com/photo-1610945265064-0e34e5519bbf?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('macbook') || keywords.includes('laptop')) {\n            return 'https://images.unsplash.com/photo-1541807084-5c52b6b3adef?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('ipad') || keywords.includes('tablet')) {\n            return 'https://images.unsplash.com/photo-1544244015-0df4b3ffc6b0?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('airpods') || keywords.includes('headphone')) {\n            return 'https://images.unsplash.com/photo-1572569511254-d8f925fe2cbb?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('sony') || keywords.includes('wh-1000')) {\n            return 'https://images.unsplash.com/photo-1583394838336-acd977736f90?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('nintendo') || keywords.includes('switch')) {\n            return 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('playstation') || keywords.includes('ps5')) {\n            return 'https://images.unsplash.com/photo-1606144042614-b2417e99c4e3?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('canon') || keywords.includes('camera')) {\n            return 'https://images.unsplash.com/photo-1502920917128-1aa500764cbd?w=400&h=400&fit=crop&crop=center';\n        } else if (keywords.includes('gopro') || keywords.includes('hero')) {\n            return 'https://images.unsplash.com/photo-1551698618-1dfe5d97d256?w=400&h=400&fit=crop&crop=center';\n        } else {\n            // Default tech product image\n            return 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=400&fit=crop&crop=center';\n        }\n    };\n    const formatPrice = (price)=>{\n        return new Intl.NumberFormat('th-TH', {\n            style: 'currency',\n            currency: 'THB'\n        }).format(price);\n    };\n    const formatRating = (rating)=>{\n        return rating.toFixed(1);\n    };\n    const formatSold = (sold)=>{\n        if (sold >= 1000) {\n            return \"\".concat((sold / 1000).toFixed(1), \"k\");\n        }\n        return sold.toString();\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"bg-white rounded-lg shadow-md overflow-hidden hover:shadow-lg transition-shadow duration-300\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"relative aspect-square\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Image, {\n                        src: product.imageUrl,\n                        alt: product.offerName,\n                        className: \"w-full h-full object-cover\",\n                        onError: (e)=>{\n                            const target = e.target;\n                            target.src = '/placeholder-image.svg';\n                        }\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 77,\n                        columnNumber: 9\n                    }, this),\n                    product.discount > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 left-2 bg-red-500 text-white px-2 py-1 rounded text-sm font-semibold\",\n                        children: [\n                            \"-\",\n                            product.discount,\n                            \"%\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 87,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"absolute top-2 right-2 bg-orange-500 text-white px-2 py-1 rounded text-sm font-semibold\",\n                        children: [\n                            product.commissionRate,\n                            \"% คอมมิชชั่น\"\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 91,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                lineNumber: 76,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"p-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"font-semibold text-gray-800 mb-2 line-clamp-2 text-sm\",\n                        children: product.offerName\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 98,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600 text-xs mb-2\",\n                        children: product.shopName\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 103,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center gap-2 mb-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-orange-600 font-bold text-lg\",\n                                children: formatPrice(product.currentPrice)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 107,\n                                columnNumber: 11\n                            }, this),\n                            product.originalPrice > product.currentPrice && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-gray-400 line-through text-sm\",\n                                children: formatPrice(product.originalPrice)\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 111,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 106,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between text-sm text-gray-600 mb-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-1\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"text-yellow-500\",\n                                        children: \"★\"\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                        lineNumber: 120,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        children: formatRating(product.rating)\n                                    }, void 0, false, {\n                                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 119,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                children: [\n                                    formatSold(product.sold),\n                                    \" ขายแล้ว\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 123,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 118,\n                        columnNumber: 9\n                    }, this),\n                    product.categoryName && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"mb-3\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"inline-block bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs\",\n                            children: product.categoryName\n                        }, void 0, false, {\n                            fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                            lineNumber: 129,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 128,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"a\", {\n                                href: product.productUrl,\n                                target: \"_blank\",\n                                rel: \"noopener noreferrer\",\n                                className: \"flex-1 bg-orange-500 hover:bg-orange-600 text-white py-2 px-4 rounded text-sm font-medium text-center transition-colors\",\n                                children: \"ดูสินค้า\"\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 137,\n                                columnNumber: 11\n                            }, this),\n                            onGetShortLink && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: handleGetShortLink,\n                                disabled: isLoading,\n                                className: \"bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white py-2 px-4 rounded text-sm font-medium transition-colors\",\n                                children: isLoading ? '...' : 'ลิงก์สั้น'\n                            }, void 0, false, {\n                                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                                lineNumber: 146,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                        lineNumber: 136,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n                lineNumber: 97,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\components\\\\ProductCard.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, this);\n}\n_s(ProductCard, \"EmvgwIb3cHpoFpeP+WmEDbjx4y4=\");\n_c = ProductCard;\nvar _c;\n$RefreshReg$(_c, \"ProductCard\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/ProductCard.tsx\n"));

/***/ })

});