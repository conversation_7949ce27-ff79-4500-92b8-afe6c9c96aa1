/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/shopee/offers/route";
exports.ids = ["app/api/shopee/offers/route"];
exports.modules = {

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fshopee%2Foffers%2Froute&page=%2Fapi%2Fshopee%2Foffers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshopee%2Foffers%2Froute.ts&appDir=H%3A%5CPROJECT%5Cshopeeproduct%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CPROJECT%5Cshopeeproduct&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fshopee%2Foffers%2Froute&page=%2Fapi%2Fshopee%2Foffers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshopee%2Foffers%2Froute.ts&appDir=H%3A%5CPROJECT%5Cshopeeproduct%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CPROJECT%5Cshopeeproduct&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \*************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   workAsyncStorage: () => (/* binding */ workAsyncStorage),\n/* harmony export */   workUnitAsyncStorage: () => (/* binding */ workUnitAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/route-kind */ \"(rsc)/./node_modules/next/dist/server/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var H_PROJECT_shopeeproduct_src_app_api_shopee_offers_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./src/app/api/shopee/offers/route.ts */ \"(rsc)/./src/app/api/shopee/offers/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/shopee/offers/route\",\n        pathname: \"/api/shopee/offers\",\n        filename: \"route\",\n        bundlePath: \"app/api/shopee/offers/route\"\n    },\n    resolvedPagePath: \"H:\\\\PROJECT\\\\shopeeproduct\\\\src\\\\app\\\\api\\\\shopee\\\\offers\\\\route.ts\",\n    nextConfigOutput,\n    userland: H_PROJECT_shopeeproduct_src_app_api_shopee_offers_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { workAsyncStorage, workUnitAsyncStorage, serverHooks } = routeModule;\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        workAsyncStorage,\n        workUnitAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fshopee%2Foffers%2Froute&page=%2Fapi%2Fshopee%2Foffers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshopee%2Foffers%2Froute.ts&appDir=H%3A%5CPROJECT%5Cshopeeproduct%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CPROJECT%5Cshopeeproduct&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "(rsc)/./src/app/api/shopee/offers/route.ts":
/*!********************************************!*\
  !*** ./src/app/api/shopee/offers/route.ts ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET),\n/* harmony export */   POST: () => (/* binding */ POST)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var _lib_shopee_api__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/shopee-api */ \"(rsc)/./src/lib/shopee-api.ts\");\n\n\nasync function GET(request) {\n    try {\n        const { searchParams } = new URL(request.url);\n        const params = {\n            keyword: searchParams.get('keyword') || undefined,\n            categoryId: searchParams.get('categoryId') ? parseInt(searchParams.get('categoryId')) : undefined,\n            minPrice: searchParams.get('minPrice') ? parseFloat(searchParams.get('minPrice')) : undefined,\n            maxPrice: searchParams.get('maxPrice') ? parseFloat(searchParams.get('maxPrice')) : undefined,\n            sortBy: searchParams.get('sortBy') || 'sales',\n            limit: searchParams.get('limit') ? parseInt(searchParams.get('limit')) : 20,\n            scrollId: searchParams.get('scrollId') || undefined\n        };\n        const result = await _lib_shopee_api__WEBPACK_IMPORTED_MODULE_1__.shopeeApi.searchOffers(params);\n        if (result.errors) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API Error',\n                details: result.errors\n            }, {\n                status: 400\n            });\n        }\n        // Transform productOfferV2 response to match expected offerList structure\n        const demoProducts = [\n            'iPhone 15 Pro Max 256GB',\n            'Samsung Galaxy S24 Ultra',\n            'MacBook Air M3 13\"',\n            'iPad Pro 12.9\" M4',\n            'AirPods Pro 3rd Gen',\n            'Sony WH-1000XM5',\n            'Nintendo Switch OLED',\n            'PlayStation 5 Slim',\n            'Canon EOS R6 Mark II',\n            'GoPro Hero 12 Black'\n        ];\n        const transformedData = {\n            offerList: {\n                nodes: result.data?.productOfferV2?.nodes?.map((node, index)=>({\n                        offerId: 'demo-' + Math.random().toString(36).substr(2, 9),\n                        offerName: demoProducts[index % demoProducts.length] || 'Demo Product',\n                        commissionRate: parseFloat(node.commissionRate) * 100,\n                        commissionType: 'percentage',\n                        originalPrice: Math.floor(Math.random() * 50000) + 10000,\n                        currentPrice: Math.floor(Math.random() * 40000) + 8000,\n                        discount: Math.floor(Math.random() * 50) + 10,\n                        imageUrl: '/placeholder-image.svg',\n                        productUrl: 'https://shopee.co.th',\n                        shopName: `Shop ${index + 1}`,\n                        rating: Math.round((Math.random() * 2 + 3) * 10) / 10,\n                        sold: Math.floor(Math.random() * 10000) + 100,\n                        categoryId: Math.floor(Math.random() * 10) + 1,\n                        categoryName: [\n                            'Electronics',\n                            'Fashion',\n                            'Home & Living',\n                            'Sports',\n                            'Beauty'\n                        ][Math.floor(Math.random() * 5)],\n                        brand: [\n                            'Apple',\n                            'Samsung',\n                            'Sony',\n                            'Canon',\n                            'Nintendo'\n                        ][Math.floor(Math.random() * 5)],\n                        description: `High-quality ${demoProducts[index % demoProducts.length]} with excellent features and performance.`\n                    })) || [],\n                totalCount: result.data?.productOfferV2?.nodes?.length || 0,\n                scrollId: undefined\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(transformedData);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal Server Error'\n        }, {\n            status: 500\n        });\n    }\n}\nasync function POST(request) {\n    try {\n        const body = await request.json();\n        const { keyword, categoryId, minPrice, maxPrice, sortBy, limit, scrollId } = body;\n        const params = {\n            keyword,\n            categoryId,\n            minPrice,\n            maxPrice,\n            sortBy,\n            limit,\n            scrollId\n        };\n        const result = await _lib_shopee_api__WEBPACK_IMPORTED_MODULE_1__.shopeeApi.searchOffers(params);\n        if (result.errors) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: 'API Error',\n                details: result.errors\n            }, {\n                status: 400\n            });\n        }\n        // Transform productOfferV2 response to match expected offerList structure\n        const demoProducts = [\n            'iPhone 15 Pro Max 256GB',\n            'Samsung Galaxy S24 Ultra',\n            'MacBook Air M3 13\"',\n            'iPad Pro 12.9\" M4',\n            'AirPods Pro 3rd Gen',\n            'Sony WH-1000XM5',\n            'Nintendo Switch OLED',\n            'PlayStation 5 Slim',\n            'Canon EOS R6 Mark II',\n            'GoPro Hero 12 Black'\n        ];\n        const transformedData = {\n            offerList: {\n                nodes: result.data?.productOfferV2?.nodes?.map((node, index)=>({\n                        offerId: 'demo-' + Math.random().toString(36).substr(2, 9),\n                        offerName: demoProducts[index % demoProducts.length] || 'Demo Product',\n                        commissionRate: parseFloat(node.commissionRate) * 100,\n                        commissionType: 'percentage',\n                        originalPrice: Math.floor(Math.random() * 50000) + 10000,\n                        currentPrice: Math.floor(Math.random() * 40000) + 8000,\n                        discount: Math.floor(Math.random() * 50) + 10,\n                        imageUrl: '/placeholder-image.svg',\n                        productUrl: 'https://shopee.co.th',\n                        shopName: `Shop ${index + 1}`,\n                        rating: Math.round((Math.random() * 2 + 3) * 10) / 10,\n                        sold: Math.floor(Math.random() * 10000) + 100,\n                        categoryId: Math.floor(Math.random() * 10) + 1,\n                        categoryName: [\n                            'Electronics',\n                            'Fashion',\n                            'Home & Living',\n                            'Sports',\n                            'Beauty'\n                        ][Math.floor(Math.random() * 5)],\n                        brand: [\n                            'Apple',\n                            'Samsung',\n                            'Sony',\n                            'Canon',\n                            'Nintendo'\n                        ][Math.floor(Math.random() * 5)],\n                        description: `High-quality ${demoProducts[index % demoProducts.length]} with excellent features and performance.`\n                    })) || [],\n                totalCount: result.data?.productOfferV2?.nodes?.length || 0,\n                scrollId: undefined\n            }\n        };\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(transformedData);\n    } catch (error) {\n        console.error('API Error:', error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: 'Internal Server Error'\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/api/shopee/offers/route.ts\n");

/***/ }),

/***/ "(rsc)/./src/lib/shopee-api.ts":
/*!*******************************!*\
  !*** ./src/lib/shopee-api.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   shopeeApi: () => (/* binding */ shopeeApi)\n/* harmony export */ });\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! crypto */ \"crypto\");\n/* harmony import */ var crypto__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(crypto__WEBPACK_IMPORTED_MODULE_0__);\n\nclass ShopeeApiClient {\n    constructor(){\n        this.appId = process.env.SHOPEE_APP_ID || '';\n        this.secret = process.env.SHOPEE_SECRET || '';\n        this.endpoint = process.env.SHOPEE_API_ENDPOINT || 'https://open-api.affiliate.shopee.co.th/graphql';\n    }\n    /**\n   * Generate SHA256 signature for Shopee API authentication\n   */ generateSignature(payload, timestamp) {\n        const signatureFactor = `${this.appId}${timestamp}${payload}${this.secret}`;\n        return crypto__WEBPACK_IMPORTED_MODULE_0___default().createHash('sha256').update(signatureFactor).digest('hex');\n    }\n    /**\n   * Generate authorization header\n   */ generateAuthHeader(payload) {\n        const timestamp = Math.floor(Date.now() / 1000);\n        const signature = this.generateSignature(payload, timestamp);\n        return `SHA256 Credential=${this.appId}, Timestamp=${timestamp}, Signature=${signature}`;\n    }\n    /**\n   * Make GraphQL request to Shopee API\n   */ async makeRequest(request) {\n        const payload = JSON.stringify(request);\n        const authHeader = this.generateAuthHeader(payload);\n        try {\n            const response = await fetch(this.endpoint, {\n                method: 'POST',\n                headers: {\n                    'Content-Type': 'application/json',\n                    'Authorization': authHeader\n                },\n                body: payload\n            });\n            if (!response.ok) {\n                throw new Error(`HTTP error! status: ${response.status}`);\n            }\n            const result = await response.json();\n            return result;\n        } catch (error) {\n            console.error('Shopee API request failed:', error);\n            throw error;\n        }\n    }\n    /**\n   * Get product offers using v2 API\n   */ async getBrandOffers() {\n        const query = `\n      {\n        productOfferV2 {\n          nodes {\n            commissionRate\n          }\n        }\n      }\n    `;\n        return this.makeRequest({\n            query\n        });\n    }\n    /**\n   * Search offers with parameters using v2 API\n   */ async searchOffers(params = {}) {\n        const { keyword = '', categoryId, minPrice, maxPrice, sortBy = 'sales', limit = 20, scrollId } = params;\n        // Use productOfferV2 for now as a basic implementation\n        let query = `\n      {\n        productOfferV2 {\n          nodes {\n            commissionRate\n          }\n        }\n      }\n    `;\n        return this.makeRequest({\n            query\n        });\n    }\n    /**\n   * Get short link for a product\n   */ async getShortLink(originalLink) {\n        const query = `\n      mutation {\n        shortLink(originalLink: \"${originalLink}\") {\n          shortLink\n          originalLink\n        }\n      }\n    `;\n        return this.makeRequest({\n            query\n        });\n    }\n    /**\n   * Get conversion report\n   */ async getConversionReport(purchaseTimeStart, purchaseTimeEnd, scrollId) {\n        const query = `\n      {\n        conversionReport(\n          purchaseTimeStart: ${purchaseTimeStart}\n          purchaseTimeEnd: ${purchaseTimeEnd}\n          ${scrollId ? `scrollId: \"${scrollId}\"` : ''}\n        ) {\n          nodes {\n            orderId\n            orderStatus\n            commissionAmount\n            commissionRate\n            purchaseTime\n            clickTime\n            offerName\n            shopName\n          }\n          totalCount\n          scrollId\n        }\n      }\n    `;\n        return this.makeRequest({\n            query\n        });\n    }\n}\nconst shopeeApi = new ShopeeApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvbGliL3Nob3BlZS1hcGkudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRCO0FBWTVCLE1BQU1DO0lBS0pDLGFBQWM7UUFDWixJQUFJLENBQUNDLEtBQUssR0FBR0MsUUFBUUMsR0FBRyxDQUFDQyxhQUFhLElBQUk7UUFDMUMsSUFBSSxDQUFDQyxNQUFNLEdBQUdILFFBQVFDLEdBQUcsQ0FBQ0csYUFBYSxJQUFJO1FBQzNDLElBQUksQ0FBQ0MsUUFBUSxHQUFHTCxRQUFRQyxHQUFHLENBQUNLLG1CQUFtQixJQUFJO0lBQ3JEO0lBRUE7O0dBRUMsR0FDRCxrQkFBMEJFLE9BQWUsRUFBRUMsU0FBaUIsRUFBVTtRQUNwRSxNQUFNQyxrQkFBa0IsR0FBRyxJQUFJLENBQUNYLEtBQUssR0FBR1UsWUFBWUQsVUFBVSxJQUFJLENBQUNMLE1BQU0sRUFBRTtRQUMzRSxPQUFPUCx3REFBaUIsQ0FBQyxVQUFVZ0IsTUFBTSxDQUFDRixpQkFBaUJHLE1BQU0sQ0FBQztJQUNwRTtJQUVBOztHQUVDLEdBQ0QsbUJBQTJCTCxPQUFlLEVBQVU7UUFDbEQsTUFBTUMsWUFBWU0sS0FBS0MsS0FBSyxDQUFDQyxLQUFLQyxHQUFHLEtBQUs7UUFDMUMsTUFBTUMsWUFBWSxJQUFJLENBQUNaLGlCQUFpQixDQUFDQyxTQUFTQztRQUVsRCxPQUFPLENBQUMsa0JBQWtCLEVBQUUsSUFBSSxDQUFDVixLQUFLLENBQUMsWUFBWSxFQUFFVSxVQUFVLFlBQVksRUFBRVUsV0FBVztJQUMxRjtJQUVBOztHQUVDLEdBQ0QsTUFBY0MsWUFBZUMsT0FBdUIsRUFBaUM7UUFDbkYsTUFBTWIsVUFBVWMsS0FBS0MsU0FBUyxDQUFDRjtRQUMvQixNQUFNRyxhQUFhLElBQUksQ0FBQ1Ysa0JBQWtCLENBQUNOO1FBRTNDLElBQUk7WUFDRixNQUFNaUIsV0FBVyxNQUFNQyxNQUFNLElBQUksQ0FBQ3JCLFFBQVEsRUFBRTtnQkFDMUNzQixRQUFRO2dCQUNSQyxTQUFTO29CQUNQLGdCQUFnQjtvQkFDaEIsaUJBQWlCSjtnQkFDbkI7Z0JBQ0FLLE1BQU1yQjtZQUNSO1lBRUEsSUFBSSxDQUFDaUIsU0FBU0ssRUFBRSxFQUFFO2dCQUNoQixNQUFNLElBQUlDLE1BQU0sQ0FBQyxvQkFBb0IsRUFBRU4sU0FBU08sTUFBTSxFQUFFO1lBQzFEO1lBRUEsTUFBTUMsU0FBK0IsTUFBTVIsU0FBU1MsSUFBSTtZQUN4RCxPQUFPRDtRQUNULEVBQUUsT0FBT0UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsOEJBQThCQTtZQUM1QyxNQUFNQTtRQUNSO0lBQ0Y7SUFFQTs7R0FFQyxHQUNELE1BQU1FLGlCQUE2RTtRQUNqRixNQUFNQyxRQUFRLENBQUM7Ozs7Ozs7O0lBUWYsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDbEIsV0FBVyxDQUFpQztZQUN0RGtCO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTUMsYUFBYUMsU0FBdUIsQ0FBQyxDQUFDLEVBQThEO1FBQ3hHLE1BQU0sRUFDSkMsVUFBVSxFQUFFLEVBQ1pDLFVBQVUsRUFDVkMsUUFBUSxFQUNSQyxRQUFRLEVBQ1JDLFNBQVMsT0FBTyxFQUNoQkMsUUFBUSxFQUFFLEVBQ1ZDLFFBQVEsRUFDVCxHQUFHUDtRQUVKLHVEQUF1RDtRQUN2RCxJQUFJRixRQUFRLENBQUM7Ozs7Ozs7O0lBUWIsQ0FBQztRQUVELE9BQU8sSUFBSSxDQUFDbEIsV0FBVyxDQUFpQztZQUN0RGtCO1FBQ0Y7SUFDRjtJQUVBOztHQUVDLEdBQ0QsTUFBTVUsYUFBYUMsWUFBb0IsRUFBZ0U7UUFDckcsTUFBTVgsUUFBUSxDQUFDOztpQ0FFYyxFQUFFVyxhQUFhOzs7OztJQUs1QyxDQUFDO1FBRUQsT0FBTyxJQUFJLENBQUM3QixXQUFXLENBQW1DO1lBQ3hEa0I7UUFDRjtJQUNGO0lBRUE7O0dBRUMsR0FDRCxNQUFNWSxvQkFDSkMsaUJBQXlCLEVBQ3pCQyxlQUF1QixFQUN2QkwsUUFBaUIsRUFDbUQ7UUFDcEUsTUFBTVQsUUFBUSxDQUFDOzs7NkJBR1UsRUFBRWEsa0JBQWtCOzJCQUN0QixFQUFFQyxnQkFBZ0I7VUFDbkMsRUFBRUwsV0FBVyxDQUFDLFdBQVcsRUFBRUEsU0FBUyxDQUFDLENBQUMsR0FBRyxHQUFHOzs7Ozs7Ozs7Ozs7Ozs7O0lBZ0JsRCxDQUFDO1FBRUQsT0FBTyxJQUFJLENBQUMzQixXQUFXLENBQXlDO1lBQzlEa0I7UUFDRjtJQUNGO0FBQ0Y7QUFFTyxNQUFNZSxZQUFZLElBQUl4RCxrQkFBa0IiLCJzb3VyY2VzIjpbIkg6XFxQUk9KRUNUXFxzaG9wZWVwcm9kdWN0XFxzcmNcXGxpYlxcc2hvcGVlLWFwaS50cyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgY3J5cHRvIGZyb20gJ2NyeXB0byc7XG5pbXBvcnQge1xuICBTaG9wZWVBcGlSZXNwb25zZSxcbiAgT2ZmZXJMaXN0LFxuICBCcmFuZE9mZmVyLFxuICBTaG9ydExpbmtSZXNwb25zZSxcbiAgQ29udmVyc2lvblJlcG9ydCxcbiAgU2VhcmNoUGFyYW1zLFxuICBHcmFwaFFMUmVxdWVzdCxcbiAgQXV0aEhlYWRlclxufSBmcm9tICcuL3R5cGVzJztcblxuY2xhc3MgU2hvcGVlQXBpQ2xpZW50IHtcbiAgcHJpdmF0ZSBhcHBJZDogc3RyaW5nO1xuICBwcml2YXRlIHNlY3JldDogc3RyaW5nO1xuICBwcml2YXRlIGVuZHBvaW50OiBzdHJpbmc7XG5cbiAgY29uc3RydWN0b3IoKSB7XG4gICAgdGhpcy5hcHBJZCA9IHByb2Nlc3MuZW52LlNIT1BFRV9BUFBfSUQgfHwgJyc7XG4gICAgdGhpcy5zZWNyZXQgPSBwcm9jZXNzLmVudi5TSE9QRUVfU0VDUkVUIHx8ICcnO1xuICAgIHRoaXMuZW5kcG9pbnQgPSBwcm9jZXNzLmVudi5TSE9QRUVfQVBJX0VORFBPSU5UIHx8ICdodHRwczovL29wZW4tYXBpLmFmZmlsaWF0ZS5zaG9wZWUuY28udGgvZ3JhcGhxbCc7XG4gIH1cblxuICAvKipcbiAgICogR2VuZXJhdGUgU0hBMjU2IHNpZ25hdHVyZSBmb3IgU2hvcGVlIEFQSSBhdXRoZW50aWNhdGlvblxuICAgKi9cbiAgcHJpdmF0ZSBnZW5lcmF0ZVNpZ25hdHVyZShwYXlsb2FkOiBzdHJpbmcsIHRpbWVzdGFtcDogbnVtYmVyKTogc3RyaW5nIHtcbiAgICBjb25zdCBzaWduYXR1cmVGYWN0b3IgPSBgJHt0aGlzLmFwcElkfSR7dGltZXN0YW1wfSR7cGF5bG9hZH0ke3RoaXMuc2VjcmV0fWA7XG4gICAgcmV0dXJuIGNyeXB0by5jcmVhdGVIYXNoKCdzaGEyNTYnKS51cGRhdGUoc2lnbmF0dXJlRmFjdG9yKS5kaWdlc3QoJ2hleCcpO1xuICB9XG5cbiAgLyoqXG4gICAqIEdlbmVyYXRlIGF1dGhvcml6YXRpb24gaGVhZGVyXG4gICAqL1xuICBwcml2YXRlIGdlbmVyYXRlQXV0aEhlYWRlcihwYXlsb2FkOiBzdHJpbmcpOiBzdHJpbmcge1xuICAgIGNvbnN0IHRpbWVzdGFtcCA9IE1hdGguZmxvb3IoRGF0ZS5ub3coKSAvIDEwMDApO1xuICAgIGNvbnN0IHNpZ25hdHVyZSA9IHRoaXMuZ2VuZXJhdGVTaWduYXR1cmUocGF5bG9hZCwgdGltZXN0YW1wKTtcbiAgICBcbiAgICByZXR1cm4gYFNIQTI1NiBDcmVkZW50aWFsPSR7dGhpcy5hcHBJZH0sIFRpbWVzdGFtcD0ke3RpbWVzdGFtcH0sIFNpZ25hdHVyZT0ke3NpZ25hdHVyZX1gO1xuICB9XG5cbiAgLyoqXG4gICAqIE1ha2UgR3JhcGhRTCByZXF1ZXN0IHRvIFNob3BlZSBBUElcbiAgICovXG4gIHByaXZhdGUgYXN5bmMgbWFrZVJlcXVlc3Q8VD4ocmVxdWVzdDogR3JhcGhRTFJlcXVlc3QpOiBQcm9taXNlPFNob3BlZUFwaVJlc3BvbnNlPFQ+PiB7XG4gICAgY29uc3QgcGF5bG9hZCA9IEpTT04uc3RyaW5naWZ5KHJlcXVlc3QpO1xuICAgIGNvbnN0IGF1dGhIZWFkZXIgPSB0aGlzLmdlbmVyYXRlQXV0aEhlYWRlcihwYXlsb2FkKTtcblxuICAgIHRyeSB7XG4gICAgICBjb25zdCByZXNwb25zZSA9IGF3YWl0IGZldGNoKHRoaXMuZW5kcG9pbnQsIHtcbiAgICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICAgIGhlYWRlcnM6IHtcbiAgICAgICAgICAnQ29udGVudC1UeXBlJzogJ2FwcGxpY2F0aW9uL2pzb24nLFxuICAgICAgICAgICdBdXRob3JpemF0aW9uJzogYXV0aEhlYWRlcixcbiAgICAgICAgfSxcbiAgICAgICAgYm9keTogcGF5bG9hZCxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihgSFRUUCBlcnJvciEgc3RhdHVzOiAke3Jlc3BvbnNlLnN0YXR1c31gKTtcbiAgICAgIH1cblxuICAgICAgY29uc3QgcmVzdWx0OiBTaG9wZWVBcGlSZXNwb25zZTxUPiA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKTtcbiAgICAgIHJldHVybiByZXN1bHQ7XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1Nob3BlZSBBUEkgcmVxdWVzdCBmYWlsZWQ6JywgZXJyb3IpO1xuICAgICAgdGhyb3cgZXJyb3I7XG4gICAgfVxuICB9XG5cbiAgLyoqXG4gICAqIEdldCBwcm9kdWN0IG9mZmVycyB1c2luZyB2MiBBUElcbiAgICovXG4gIGFzeW5jIGdldEJyYW5kT2ZmZXJzKCk6IFByb21pc2U8U2hvcGVlQXBpUmVzcG9uc2U8eyBwcm9kdWN0T2ZmZXJWMjogQnJhbmRPZmZlciB9Pj4ge1xuICAgIGNvbnN0IHF1ZXJ5ID0gYFxuICAgICAge1xuICAgICAgICBwcm9kdWN0T2ZmZXJWMiB7XG4gICAgICAgICAgbm9kZXMge1xuICAgICAgICAgICAgY29tbWlzc2lvblJhdGVcbiAgICAgICAgICB9XG4gICAgICAgIH1cbiAgICAgIH1cbiAgICBgO1xuXG4gICAgcmV0dXJuIHRoaXMubWFrZVJlcXVlc3Q8eyBwcm9kdWN0T2ZmZXJWMjogQnJhbmRPZmZlciB9Pih7XG4gICAgICBxdWVyeSxcbiAgICB9KTtcbiAgfVxuXG4gIC8qKlxuICAgKiBTZWFyY2ggb2ZmZXJzIHdpdGggcGFyYW1ldGVycyB1c2luZyB2MiBBUElcbiAgICovXG4gIGFzeW5jIHNlYXJjaE9mZmVycyhwYXJhbXM6IFNlYXJjaFBhcmFtcyA9IHt9KTogUHJvbWlzZTxTaG9wZWVBcGlSZXNwb25zZTx7IHByb2R1Y3RPZmZlclYyOiBCcmFuZE9mZmVyIH0+PiB7XG4gICAgY29uc3Qge1xuICAgICAga2V5d29yZCA9ICcnLFxuICAgICAgY2F0ZWdvcnlJZCxcbiAgICAgIG1pblByaWNlLFxuICAgICAgbWF4UHJpY2UsXG4gICAgICBzb3J0QnkgPSAnc2FsZXMnLFxuICAgICAgbGltaXQgPSAyMCxcbiAgICAgIHNjcm9sbElkXG4gICAgfSA9IHBhcmFtcztcblxuICAgIC8vIFVzZSBwcm9kdWN0T2ZmZXJWMiBmb3Igbm93IGFzIGEgYmFzaWMgaW1wbGVtZW50YXRpb25cbiAgICBsZXQgcXVlcnkgPSBgXG4gICAgICB7XG4gICAgICAgIHByb2R1Y3RPZmZlclYyIHtcbiAgICAgICAgICBub2RlcyB7XG4gICAgICAgICAgICBjb21taXNzaW9uUmF0ZVxuICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgfVxuICAgIGA7XG5cbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdDx7IHByb2R1Y3RPZmZlclYyOiBCcmFuZE9mZmVyIH0+KHtcbiAgICAgIHF1ZXJ5LFxuICAgIH0pO1xuICB9XG5cbiAgLyoqXG4gICAqIEdldCBzaG9ydCBsaW5rIGZvciBhIHByb2R1Y3RcbiAgICovXG4gIGFzeW5jIGdldFNob3J0TGluayhvcmlnaW5hbExpbms6IHN0cmluZyk6IFByb21pc2U8U2hvcGVlQXBpUmVzcG9uc2U8eyBzaG9ydExpbms6IFNob3J0TGlua1Jlc3BvbnNlIH0+PiB7XG4gICAgY29uc3QgcXVlcnkgPSBgXG4gICAgICBtdXRhdGlvbiB7XG4gICAgICAgIHNob3J0TGluayhvcmlnaW5hbExpbms6IFwiJHtvcmlnaW5hbExpbmt9XCIpIHtcbiAgICAgICAgICBzaG9ydExpbmtcbiAgICAgICAgICBvcmlnaW5hbExpbmtcbiAgICAgICAgfVxuICAgICAgfVxuICAgIGA7XG5cbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdDx7IHNob3J0TGluazogU2hvcnRMaW5rUmVzcG9uc2UgfT4oe1xuICAgICAgcXVlcnksXG4gICAgfSk7XG4gIH1cblxuICAvKipcbiAgICogR2V0IGNvbnZlcnNpb24gcmVwb3J0XG4gICAqL1xuICBhc3luYyBnZXRDb252ZXJzaW9uUmVwb3J0KFxuICAgIHB1cmNoYXNlVGltZVN0YXJ0OiBudW1iZXIsXG4gICAgcHVyY2hhc2VUaW1lRW5kOiBudW1iZXIsXG4gICAgc2Nyb2xsSWQ/OiBzdHJpbmdcbiAgKTogUHJvbWlzZTxTaG9wZWVBcGlSZXNwb25zZTx7IGNvbnZlcnNpb25SZXBvcnQ6IENvbnZlcnNpb25SZXBvcnQgfT4+IHtcbiAgICBjb25zdCBxdWVyeSA9IGBcbiAgICAgIHtcbiAgICAgICAgY29udmVyc2lvblJlcG9ydChcbiAgICAgICAgICBwdXJjaGFzZVRpbWVTdGFydDogJHtwdXJjaGFzZVRpbWVTdGFydH1cbiAgICAgICAgICBwdXJjaGFzZVRpbWVFbmQ6ICR7cHVyY2hhc2VUaW1lRW5kfVxuICAgICAgICAgICR7c2Nyb2xsSWQgPyBgc2Nyb2xsSWQ6IFwiJHtzY3JvbGxJZH1cImAgOiAnJ31cbiAgICAgICAgKSB7XG4gICAgICAgICAgbm9kZXMge1xuICAgICAgICAgICAgb3JkZXJJZFxuICAgICAgICAgICAgb3JkZXJTdGF0dXNcbiAgICAgICAgICAgIGNvbW1pc3Npb25BbW91bnRcbiAgICAgICAgICAgIGNvbW1pc3Npb25SYXRlXG4gICAgICAgICAgICBwdXJjaGFzZVRpbWVcbiAgICAgICAgICAgIGNsaWNrVGltZVxuICAgICAgICAgICAgb2ZmZXJOYW1lXG4gICAgICAgICAgICBzaG9wTmFtZVxuICAgICAgICAgIH1cbiAgICAgICAgICB0b3RhbENvdW50XG4gICAgICAgICAgc2Nyb2xsSWRcbiAgICAgICAgfVxuICAgICAgfVxuICAgIGA7XG5cbiAgICByZXR1cm4gdGhpcy5tYWtlUmVxdWVzdDx7IGNvbnZlcnNpb25SZXBvcnQ6IENvbnZlcnNpb25SZXBvcnQgfT4oe1xuICAgICAgcXVlcnksXG4gICAgfSk7XG4gIH1cbn1cblxuZXhwb3J0IGNvbnN0IHNob3BlZUFwaSA9IG5ldyBTaG9wZWVBcGlDbGllbnQoKTtcbiJdLCJuYW1lcyI6WyJjcnlwdG8iLCJTaG9wZWVBcGlDbGllbnQiLCJjb25zdHJ1Y3RvciIsImFwcElkIiwicHJvY2VzcyIsImVudiIsIlNIT1BFRV9BUFBfSUQiLCJzZWNyZXQiLCJTSE9QRUVfU0VDUkVUIiwiZW5kcG9pbnQiLCJTSE9QRUVfQVBJX0VORFBPSU5UIiwiZ2VuZXJhdGVTaWduYXR1cmUiLCJwYXlsb2FkIiwidGltZXN0YW1wIiwic2lnbmF0dXJlRmFjdG9yIiwiY3JlYXRlSGFzaCIsInVwZGF0ZSIsImRpZ2VzdCIsImdlbmVyYXRlQXV0aEhlYWRlciIsIk1hdGgiLCJmbG9vciIsIkRhdGUiLCJub3ciLCJzaWduYXR1cmUiLCJtYWtlUmVxdWVzdCIsInJlcXVlc3QiLCJKU09OIiwic3RyaW5naWZ5IiwiYXV0aEhlYWRlciIsInJlc3BvbnNlIiwiZmV0Y2giLCJtZXRob2QiLCJoZWFkZXJzIiwiYm9keSIsIm9rIiwiRXJyb3IiLCJzdGF0dXMiLCJyZXN1bHQiLCJqc29uIiwiZXJyb3IiLCJjb25zb2xlIiwiZ2V0QnJhbmRPZmZlcnMiLCJxdWVyeSIsInNlYXJjaE9mZmVycyIsInBhcmFtcyIsImtleXdvcmQiLCJjYXRlZ29yeUlkIiwibWluUHJpY2UiLCJtYXhQcmljZSIsInNvcnRCeSIsImxpbWl0Iiwic2Nyb2xsSWQiLCJnZXRTaG9ydExpbmsiLCJvcmlnaW5hbExpbmsiLCJnZXRDb252ZXJzaW9uUmVwb3J0IiwicHVyY2hhc2VUaW1lU3RhcnQiLCJwdXJjaGFzZVRpbWVFbmQiLCJzaG9wZWVBcGkiXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/shopee-api.ts\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true!":
/*!******************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?server=true! ***!
  \******************************************************************************************************/
/***/ (() => {



/***/ }),

/***/ "../app-render/after-task-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist/server/app-render/after-task-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/after-task-async-storage.external.js");

/***/ }),

/***/ "../app-render/work-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/server/app-render/work-async-storage.external.js" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-async-storage.external.js");

/***/ }),

/***/ "./work-unit-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist/server/app-render/work-unit-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/server/app-render/work-unit-async-storage.external.js");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader/index.js?name=app%2Fapi%2Fshopee%2Foffers%2Froute&page=%2Fapi%2Fshopee%2Foffers%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fshopee%2Foffers%2Froute.ts&appDir=H%3A%5CPROJECT%5Cshopeeproduct%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=H%3A%5CPROJECT%5Cshopeeproduct&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();