// Shopee API Types
export interface ShopeeApiResponse<T> {
  data?: T;
  errors?: ShopeeApiError[];
}

export interface ShopeeApiError {
  message: string;
  path?: string;
  extensions?: {
    code: number;
    message: string;
  };
}

// Offer Types
export interface OfferNode {
  offerId: string;
  offerName: string;
  commissionRate: number;
  commissionType: string;
  originalPrice: number;
  currentPrice: number;
  discount: number;
  imageUrl: string;
  productUrl: string;
  shopName: string;
  rating: number;
  sold: number;
  categoryId: number;
  categoryName: string;
  brand?: string;
  description?: string;
}

export interface OfferList {
  nodes: OfferNode[];
  totalCount: number;
  scrollId?: string;
}

export interface BrandOffer {
  nodes: {
    commissionRate: number;
    offerName: string;
    offerId: string;
    imageUrl: string;
    originalPrice: number;
    currentPrice: number;
    shopName: string;
  }[];
}

// Short Link Types
export interface ShortLinkResponse {
  shortLink: string;
  originalLink: string;
}

// Conversion Report Types
export interface ConversionReport {
  nodes: ConversionNode[];
  totalCount: number;
  scrollId?: string;
}

export interface ConversionNode {
  orderId: string;
  orderStatus: string;
  commissionAmount: number;
  commissionRate: number;
  purchaseTime: number;
  clickTime: number;
  offerName: string;
  shopName: string;
}

// Search Parameters
export interface SearchParams {
  keyword?: string;
  categoryId?: number;
  minPrice?: number;
  maxPrice?: number;
  sortBy?: 'price_asc' | 'price_desc' | 'sales' | 'rating';
  limit?: number;
  scrollId?: string;
}

// API Request Types
export interface GraphQLRequest {
  query: string;
  variables?: Record<string, any>;
  operationName?: string;
}

export interface AuthHeader {
  credential: string;
  timestamp: number;
  signature: string;
}
